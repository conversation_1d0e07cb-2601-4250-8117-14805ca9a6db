import React from 'react';
import { htmlParser } from './src/utils/htmlParser';

// Test the HTML parser with the problematic HTML
const testHtml = '<p>most <strong>people</strong></p>';

const TestComponent = () => {
  const result = htmlParser(testHtml, {});
  
  console.log('Parsed result:', result);
  
  return (
    <div>
      <h2>Original HTML:</h2>
      <pre>{testHtml}</pre>
      
      <h2>Parsed Result:</h2>
      {result}
    </div>
  );
};

export default TestComponent;
