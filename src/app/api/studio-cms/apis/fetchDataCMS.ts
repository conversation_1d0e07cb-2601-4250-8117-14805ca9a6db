import axios from "axios";

export const fetchStudioCmsData = async (query: string, operationName: string, variables: unknown) => {
  
  // Check if variables contain an 'id' or 'ids' property, or if variables exist at all
  const hasIdOrIds =
    variables &&
    typeof variables === "object" &&
    ("id" in variables ||
      "ids" in variables ||
      Object.keys(variables as object).length > 0);

  const requestBody: any = {
    query: query,
    operationName: operationName,
  };
  
  if (hasIdOrIds) {
    requestBody.variables = variables;
  }

  try{
    const response = await axios.post(
      process.env.GRAPHQL_API || "",
      requestBody,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.GRAPHQL_TOKEN}`,
        },
      }
    );

    return response.data;

  }catch(error){
    console.error("Error fetching data:", error);
    throw error;
  }

  

  
};