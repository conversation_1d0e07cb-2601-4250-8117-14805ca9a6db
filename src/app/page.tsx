import Home from "@/components/Home";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Compare & Buy Health & Term Insurance Online - OneAssure",
  description:
    "Compare, buy, and renew health and term insurance policies for yourself and your family with OneAssure. Get instant insurance quotes from various insurers in India and specially curated insurance plans for yourself",
  openGraph: {
    title: "Oneassure",
    description:
      "Compare, buy, and renew health and term insurance policies for yourself and your family with OneAssure. Get instant insurance quotes from various insurers in India and specially curated insurance plans for yourself",
    type: "website",
    url: "https://www.oneassure.in",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/",
  },
};

async function getData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/home-landing-page?populate[hero][fields][0]=title&populate[hero][fields][1]=description&populate[hero][populate][insuranceType][fields][0]=title&populate[hero][populate][insuranceType][fields][1]=url&populate[hero][populate][insuranceType][populate][logo][fields][0]=url&populate[policyVerticals][populate][insuranceTypeCard][fields][0]=title&populate[policyVerticals][populate][insuranceTypeCard][fields][1]=description&populate[policyVerticals][populate][insuranceTypeCard][fields][2]=url&populate[policyVerticals][populate][insuranceTypeCard][populate][logo][fields][0]=url&populate[ourOffering][fields][0]=title&populate[ourOffering][fields][1]=description&populate[ourOffering][populate][offers][fields][0]=title&populate[ourOffering][populate][offers][fields][1]=description&populate[ourOffering][populate][offers][populate][logo][fields][0]=url&populate[talkToExpert][fields][0]=title&populate[talkToExpert][fields][1]=subTitle&populate[faq][fields][0]=title&populate[faq][fields][1]=subTitle&populate[faq][populate][faqs][fields][0]=question&populate[faq][populate][faqs][fields][1]=ans&populate[testimonials][populate][testimonial][fields][0]=name&populate[testimonials][populate][testimonial][fields][1]=statement&populate[testimonials][populate][testimonial][fields][2]=backgroundColor&populate[testimonials][populate][testimonial][populate][thumbnail][fields][0]=url&populate[whyOneAssure][fields][0]=title&populate[whyOneAssure][fields][1]=subTitle&populate[whyOneAssure][populate][points][fields][0]=number&populate[whyOneAssure][populate][points][fields][1]=title&populate[whyOneAssure][populate][points][fields][2]=subTitle&populate[oneAssureInNews][populate][newsCard][fields][0]=url&populate[oneAssureInNews][populate][newsCard][populate][logo][fields][0]=url&populate[products][populate][health_variants][fields][0]=name&populate[products][populate][health_variants][fields][1]=verdict&populate[products][populate][health_variants][fields][2]=slug&populate[products][populate][health_variants][populate][company][fields][0]=name&populate[products][populate][health_variants][populate][company][fields][1]=slug&populate[products][populate][health_variants][populate][company][fields][2]=category&populate[products][populate][health_variants][populate][company][populate][logo][fields][0]=url&populate[products][populate][term_variants][fields][0]=name&populate[products][populate][term_variants][fields][1]=verdict&populate[products][populate][term_variants][fields][2]=slug&populate[products][populate][term_variants][populate][company][fields][0]=name&populate[products][populate][term_variants][populate][company][fields][1]=slug&populate[products][populate][term_variants][populate][company][fields][2]=category&populate[products][populate][term_variants][populate][company][populate][logo][fields][0]=url`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res;
}
async function getPartners() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?populate[logo][fields][0]=url&populate[health_variants][fields][0]=name&populate[health_variants][fields][1]=slug&populate[term_variants][fields][0]=name&populate[term_variants][fields][1]=slug`,
    { headers }
  ).then((res) => res.json());
  return res;
}

export default async function HomePage() {
  const data = await getData();
  const partners = await getPartners();
  return <Home data={data?.data} partners={partners.data} />;
}
