"use client";
import React, { useRef, useState, useEffect } from "react";

import { MessageType, OnboardingState } from "@/components/Chat/types/types";
import { ChatMessages } from "@/components/Chat/components/ChatMessages";
import { ChatInput } from "@/components/Chat/components/ChatInput";
import { ChatHeader } from "@/components/Chat/components/ChatHeader";
import {
  useInitiateVerification,
  useVerifyOTP,
} from "@/components/Chat/api/phoneVerification";
import { Policy } from "@/components/Chat/api/policyFetch";
import {
  PolicyDetails,
  useLinkPolicyToCustomer,
} from "@/components/Chat/api/policyWorkflow";
import { useSendChatQuery } from "@/components/Chat/api/chatQuery";
import { useGenerateLead } from "@/components/Chat/api/leadGeneration";

// OTP Countdown Timer Component - now designed as a message component
const OtpCountdown = ({
  expiryMinutes = 10,
  onResend,
  phoneLastDigits,
}: {
  expiryMinutes?: number;
  onResend: () => void;
  phoneLastDigits: string;
}) => {
  const [timeLeft, setTimeLeft] = useState(expiryMinutes * 60);
  const [isExpired, setIsExpired] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Fix issue with component being unmounted during verification
  useEffect(() => {
    setMounted(true);
    return () => {
      // Removed console.log
    };
  }, []);

  // Reset timer when phoneLastDigits changes (new code sent)
  useEffect(() => {
    if (mounted) {
      setTimeLeft(expiryMinutes * 60);
      setIsExpired(false);
    }
  }, [phoneLastDigits, expiryMinutes, mounted]);

  useEffect(() => {
    if (!mounted) return;

    if (timeLeft <= 0) {
      setIsExpired(true);
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, mounted]);

  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;

  const formattedTime = `${minutes}:${seconds.toString().padStart(2, "0")}`;

  if (isExpired) {
    return (
      <div className="flex flex-col mt-2">
        <p className="text-red-600 text-sm mb-1">
          The verification code has expired.
        </p>
        <button
          onClick={onResend}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium bg-blue-50 self-start px-3 py-1.5 rounded-md"
        >
          Send a new code
        </button>
      </div>
    );
  }

  return (
    <div className="text-sm text-gray-600 mt-1">
      <p>
        I&apos;ve sent a 6-digit verification code to your phone number ending
        in {phoneLastDigits}.
      </p>
      <p className="mt-1">
        Code expires in <span className="font-medium">{formattedTime}</span>
        {timeLeft <= expiryMinutes * 60 - 30 && (
          <button
            onClick={onResend}
            className="text-blue-600 hover:text-blue-800 font-medium ml-3"
          >
            Resend code
          </button>
        )}
      </p>
    </div>
  );
};

export default function AIChatPage() {
  const [messages, setMessages] = useState<MessageType[]>([
    { type: "ai", text: "Welcome to OneAssure! How can I help you today?" },
    { type: "select-customer" },
  ]);
  const [input, setInput] = useState("");
  const [onboarding, setOnboarding] = useState<OnboardingState>("select");
  const [phone, setPhone] = useState("");
  const [otpToken, setOtpToken] = useState("");
  const [policyFile, setPolicyFile] = useState<File | null>(null);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [currentVariantId, setCurrentVariantId] = useState<string | undefined>(
    undefined
  );
  const chatAreaRef = useRef<HTMLDivElement>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [freeMessageCount, setFreeMessageCount] = useState(0);
  const [isNewCustomerWithUpload, setIsNewCustomerWithUpload] = useState(false);
  const [requireVerification, setRequireVerification] = useState(false);
  const [reachedMessageLimit, setReachedMessageLimit] = useState(false);
  const [isProductSelection, setIsProductSelection] = useState(false);
  const [productSelected, setProductSelected] = useState(false);
  const [selectedProductInfo, setSelectedProductInfo] = useState<{
    insurer_name: string;
    product_name: string;
  } | null>(null);
  const [suggestionSelected, setSuggestionSelected] = useState(false);
  const [customerSelected, setCustomerSelected] = useState(false);
  const [productSelectionCompleted, setProductSelectionCompleted] =
    useState(false);
  const [policyDocumentChoiceMade, setPolicyDocumentChoiceMade] =
    useState(false);
  const [policySelectionCompleted, setPolicySelectionCompleted] =
    useState(false);
  const MAX_FREE_MESSAGES = 3;

  // Initialize mutations
  const initiateVerificationMutation = useInitiateVerification();
  const verifyOTPMutation = useVerifyOTP();
  const sendChatQueryMutation = useSendChatQuery();
  const linkPolicyMutation = useLinkPolicyToCustomer();
  const generateLeadMutation = useGenerateLead();

  function handleSelect(type: "existing" | "new") {
    setCustomerSelected(true);
    if (type === "existing") {
      setMessages((msgs) => [
        ...msgs,
        { type: "user", text: "I'm an Existing Customer" },
        { type: "ai", text: "Please enter your 10-digit phone number:" },
      ]);
      setOnboarding("existing-phone");
    } else {
      setMessages((msgs) => [
        ...msgs,
        { type: "user", text: "I'm a New Customer" },
        { type: "policy-document-choice" },
      ]);
      setOnboarding("new-policy-choice");
    }
  }

  function handlePolicyDocumentChoice(hasDocument: boolean) {
    setPolicyDocumentChoiceMade(true);
    if (hasDocument) {
      setMessages((msgs) => [
        ...msgs,
        { type: "user", text: "Yes, I have a policy document" },
        {
          type: "ai",
          text: "Please upload your insurance policy document for analysis:",
        },
        { type: "policy-upload" },
      ]);
      setOnboarding("new-upload");
    } else {
      setMessages((msgs) => [
        ...msgs,
        { type: "user", text: "No, help me explore products" },
        {
          type: "ai",
          text: "I'll help you find the right insurance product. Please select from our available options:",
        },
        { type: "product-catalog" },
      ]);
      setOnboarding("new-catalog");
    }
  }

  function handleProductSelected(product: {
    product_id: string;
    product_name: string;
    variant_id: string;
    insurer_name: string;
  }) {
    // Store the selected product information
    setCurrentVariantId(product.variant_id);
    sessionStorage.setItem("variant_id", product.variant_id);

    // Mark this as a product selection flow (not policy upload)
    setIsProductSelection(true);
    setProductSelected(true);
    setProductSelectionCompleted(true);

    // Store product info for lead generation
    setSelectedProductInfo({
      insurer_name: product.insurer_name,
      product_name: product.product_name,
    });

    setMessages((msgs) => [
      ...msgs,
      {
        type: "ai",
        text: `Great choice! You've selected ${product.product_name} from ${product.insurer_name}. I'm ready to help you with any questions about this insurance product. What would you like to know?`,
      },
      { type: "policy-suggestions" },
    ]);
    setOnboarding("policy-suggestions");
  }

  function handleSuggestionClick(suggestion: string) {
    // Mark suggestion as selected
    setSuggestionSelected(true);

    // Add user message with the selected suggestion
    setMessages((msgs) => [...msgs, { type: "user", text: suggestion }]);

    // Set to done state to enable chat
    setOnboarding("done");

    // Auto-send the suggestion as a message
    setInput("");
    handleSendSuggestion(suggestion);
  }

  async function handleSendSuggestion(suggestion: string) {
    // Add loading indicator
    setMessages((prev) => [...prev, { type: "loading" }]);

    try {
      // Get policy ID from sessionStorage if available
      const policyId = sessionStorage.getItem("policyId");

      // Get variant ID from sessionStorage if available
      let variantIdToSend = currentVariantId;
      if (!variantIdToSend) {
        variantIdToSend = sessionStorage.getItem("variant_id") || undefined;
      }

      // Send query to AI chat service
      const response = await sendChatQueryMutation.mutateAsync({
        query: suggestion,
        session_id: sessionId,
        variant_id: variantIdToSend,
        agent_type: "function",
        policy_id: policyId || undefined,
      });

      // Update session ID for future requests
      if (response.payload.session_id) {
        setSessionId(response.payload.session_id);
      }

      // Remove loading indicator and add AI response as markdown
      setMessages((prev) => [
        ...prev.filter((msg) => msg.type !== "loading"),
        {
          type: "ai-markdown",
          text: response.payload.response,
        },
      ]);
    } catch (error) {
      // Remove loading indicator and add error message with Calendly fallback
      const errorMessage =
        "Sorry, I couldn't process your request. Please try again.";

      const errorWithFallback = `${errorMessage}\n\nIf you continue to experience issues, you can [book a call with our team](https://calendly.com/oneassure/30min) for personalized assistance.`;

      setMessages((prev) => [
        ...prev.filter((msg) => msg.type !== "loading"),
        {
          type: "ai-markdown",
          text: errorWithFallback,
        },
      ]);
    }
  }

  function handlePolicyUpload(
    file: File,
    documentKey?: string,
    policyDetails?: PolicyDetails
  ) {
    // Store the uploaded file and mark this as an upload flow
    setPolicyFile(file);
    setIsNewCustomerWithUpload(true);

    // Show loading message
    setMessages((prev) => [...prev, { type: "loading" }]);

    // Check if there was an error parsing the policy (no policy details provided)
    if (!policyDetails) {
      const errorMessage =
        "I'm sorry, but I couldn't analyze your policy document. This can happen with certain document formats or quality issues.\n\nFor assistance with your policy analysis, please contact our support <NAME_EMAIL> or [book a call with our team](https://calendly.com/oneassure/30min) for personalized assistance.";

      setMessages((prev) => [
        ...prev.filter((msg) => msg.type !== "loading"),
        {
          type: "ai-markdown",
          text: errorMessage,
        },
      ]);
      setOnboarding("upload-error");
      return;
    }

    // Create a detailed message based on policy details if available
    let message = `Perfect! I've successfully analyzed your ${policyDetails.insurer_details.product_name} policy from ${policyDetails.insurer_details.insurer_name}.`;

    if (policyDetails.policy_details.sum_insured) {
      const formatPolicyAmount = (value: string): string => {
        const amount = parseFloat(value);
        return isNaN(amount)
          ? "Not available"
          : `₹${amount.toLocaleString("en-IN")}`;
      };

      message += ` Your policy has a sum insured of ${formatPolicyAmount(
        policyDetails.policy_details.sum_insured
      )}.`;
    }

    message +=
      " I'm ready to help you with any questions about your coverage. What would you like to know?";

    // Set currentVariantId from policyDetails.insurer_details.variant_id if available
    if (policyDetails.insurer_details?.variant_id) {
      setCurrentVariantId(policyDetails.insurer_details.variant_id);
    }

    setMessages((prev) => [
      ...prev.filter((msg) => msg.type !== "loading"),
      {
        type: "ai",
        text: message,
      },
      { type: "policy-suggestions" },
    ]);

    // Set to policy-suggestions state instead of done
    setOnboarding("policy-suggestions");

    // Only store policy_id in sessionStorage if available
    if (policyDetails && sessionStorage.getItem("policyId")) {
      sessionStorage.setItem(
        "policyId",
        sessionStorage.getItem("policyId") || ""
      );
    }

    setShowSuccessToast(true);
    setTimeout(() => setShowSuccessToast(false), 3000);
  }

  function handlePolicySelected(policy: Policy) {
    // Check if this is a reset flow request
    if (policy.policy_id === "reset_flow" && "reset_to_selection" in policy) {
      // Clear selected policy
      setSelectedPolicy(null);
      sessionStorage.removeItem("selectedPolicyId");

      // Reset to customer selection step
      setMessages((prev) => [
        ...prev,
        {
          type: "ai",
          text: "Let's try a different approach. Are you an existing customer or a new customer?",
        },
        {
          type: "select-customer",
        },
      ]);

      // Reset onboarding state
      setOnboarding("select");
      return;
    }

    // Mark policy selection as completed
    setPolicySelectionCompleted(true);
    setSelectedPolicy(policy);

    // Show loading while "analyzing" the policy
    setMessages((prev) => [...prev, { type: "loading" }]);

    // Create a detailed message for the selected policy
    const formatCurrency = (value: string): string => {
      const amount = parseFloat(value);
      return isNaN(amount)
        ? "Not available"
        : `₹${amount.toLocaleString("en-IN")}`;
    };

    let message = `Perfect! I've loaded your ${policy.product_name} policy from ${policy.insurer_name}.`;

    if (policy.sum_insured) {
      message += ` Your policy has a sum insured of ${formatCurrency(
        policy.sum_insured
      )}.`;
    }

    message +=
      " I'm ready to help you with any questions about your coverage. What would you like to know?";

    setMessages((prev) => [
      ...prev.filter((msg) => msg.type !== "loading"),
      {
        type: "ai",
        text: message,
      },
      { type: "policy-suggestions" },
    ]);

    // Only store policyId and variant_id in sessionStorage
    sessionStorage.setItem("policyId", policy.policy_id);
    if (policy.variant_id) {
      sessionStorage.setItem("variant_id", policy.variant_id);
    }

    // Set to policy-suggestions state instead of done
    setOnboarding("policy-suggestions");
  }

  // Create a modified handleStartVerification function for a chat-based verification flow
  function handleStartVerification() {
    // Removed console.log

    // Add AI message asking for phone number in a conversational way
    setMessages((msgs) => [
      ...msgs,
      {
        type: "ai",
        text: "To continue our conversation, I need to verify your phone number. Please enter your 10-digit mobile number below:",
      },
    ]);

    // Set onboarding state to expect phone number input
    setOnboarding("new-customer-phone");
  }

  // Modify the handleSend function for OTP verification flow
  async function handleSend(e?: React.FormEvent) {
    if (e) e.preventDefault();
    const trimmed = input.trim();
    if (!trimmed) return;

    // Check which step we're in
    if (
      onboarding === "existing-phone" ||
      onboarding === "new-customer-phone"
    ) {
      // Validate phone number
      const phoneRegex = /^[0-9]{10}$/;
      if (!phoneRegex.test(trimmed)) {
        setMessages((msgs) => [
          ...msgs,
          { type: "user", text: trimmed },
          {
            type: "ai",
            text: "Sorry, I need a valid 10-digit phone number. Please try again with just the digits (no spaces or special characters).",
          },
        ]);
        setInput("");
        return;
      }

      setPhone(trimmed);
      // Add user message first
      setMessages((msgs) => [...msgs, { type: "user", text: trimmed }]);
      setInput("");

      // Show loading indicator
      setMessages((prev) => [...prev, { type: "loading" }]);

      // Initiate verification
      initiateVerificationMutation.mutate(
        { phone_number: trimmed, scopes: ["CHAT"] },
        {
          onSuccess: (response) => {
            // Store OTP token for later OTP verification
            if (response.payload?.otpToken) {
              setOtpToken(response.payload.otpToken);
            }

            const countdownKey = `otp-${Date.now()}`;

            // Update UI on success with integrated countdown
            setMessages((msgs) => [
              ...msgs.filter((msg) => msg.type !== "loading"),
              {
                type: "ai",
                text: `Perfect! Please enter the 6-digit verification code sent to your phone number ending in ${trimmed.slice(
                  -4
                )}:`,
                component: (
                  <OtpCountdown
                    key={countdownKey}
                    onResend={handleResendOtp}
                    phoneLastDigits={trimmed.slice(-4)}
                  />
                ),
              },
            ]);

            // No need for separate countdown display
            setOnboarding(
              onboarding === "existing-phone"
                ? "existing-otp"
                : "new-customer-otp"
            );
          },
          onError: (error) => {
            // Handle error with more conversational language
            setMessages((msgs) => [
              ...msgs.filter((msg) => msg.type !== "loading"),
              {
                type: "ai",
                text:
                  error instanceof Error
                    ? error.message
                    : "I couldn't send the verification code to that number. Please check the number and try again, or use a different mobile number.",
              },
            ]);
          },
        }
      );
    } else if (
      onboarding === "existing-otp" ||
      onboarding === "new-customer-otp"
    ) {
      // Validate OTP
      const otpRegex = /^[0-9]{6}$/;
      if (!otpRegex.test(trimmed)) {
        setMessages((msgs) => [
          ...msgs,
          { type: "user", text: trimmed },
          {
            type: "ai",
            text: "I need the 6-digit code sent to your phone. Please enter all 6 digits without any spaces.",
          },
        ]);
        setInput("");
        return;
      }

      // Add user message first
      setMessages((msgs) => [...msgs, { type: "user", text: trimmed }]);
      setInput("");

      // Show loading indicator
      setMessages((prev) => [...prev, { type: "loading" }]);

      // Verify OTP
      verifyOTPMutation.mutate(
        {
          phone_number: phone,
          otp: trimmed,
          otp_token: otpToken,
        },
        {
          onSuccess: (response) => {
            // Store the access token in sessionStorage if present
            if (response.payload?.accessToken) {
              sessionStorage.setItem(
                "accessToken",
                response.payload.accessToken
              );
            }

            if (onboarding === "existing-otp") {
              // For existing customer flow - show policy selection
              setMessages((msgs) => [
                ...msgs.filter((msg) => msg.type !== "loading"),
                {
                  type: "ai",
                  text: "Thanks! Your phone number is now verified. Here are your policies:",
                },
                {
                  type: "policy-selection",
                  phone: phone,
                },
              ]);
              setOnboarding("existing-policy");
              setRequireVerification(false);
            } else {
              // For new customer verification after message limit
              // First show success message in a conversational way
              setMessages((msgs) => [
                ...msgs.filter((msg) => msg.type !== "loading"),
                {
                  type: "ai",
                  text: "Great! Your phone number is now verified. Let's continue our conversation. What would you like to know about your insurance policy?",
                },
              ]);

              // Add a subtle success indicator in the top-right
              setShowSuccessToast(true);

              setReachedMessageLimit(false);
              setRequireVerification(false);
              setOnboarding("done");

              // Handle different flows after verification
              if (isProductSelection && selectedProductInfo && sessionId) {
                // For product selection users, generate a lead
                const accessToken = sessionStorage.getItem("accessToken");
                if (accessToken) {
                  generateLeadMutation.mutate({
                    payload: {
                      insurer_name: selectedProductInfo.insurer_name,
                      product_name: selectedProductInfo.product_name,
                      session_id: sessionId,
                    },
                    accessToken: accessToken,
                  });
                }
              } else {
                // For policy upload users, link policy to customer
                const policyId = sessionStorage.getItem("policyId");
                const accessToken = sessionStorage.getItem("accessToken");
                if (policyId && accessToken) {
                  linkPolicyMutation.mutate({
                    policy_id: policyId,
                    accessToken,
                  });
                }
              }
            }
          },
          onError: (error) => {
            // Handle error with a conversational approach
            setMessages((msgs) => [
              ...msgs.filter((msg) => msg.type !== "loading"),
              {
                type: "ai",
                text:
                  error instanceof Error
                    ? error.message
                    : "That doesn't seem to be the right code. Please check the verification code and try again. The code is valid for 10 minutes.",
              },
            ]);
          },
        }
      );
    } else if (onboarding === "new-upload") {
      // For new customers providing details instead of upload
      setMessages((msgs) => [...msgs, { type: "user", text: trimmed }]);
      setInput("");

      // Add loading message and response immediately
      setMessages((prev) => [...prev, { type: "loading" }]);

      setMessages((msgs) => [
        ...msgs.filter((msg) => msg.type !== "loading"),
        {
          type: "ai",
          text: "Thank you for providing your details. You can now chat with the AI Assistant.",
        },
      ]);
      setOnboarding("done");
    } else if (onboarding === "policy-suggestions") {
      // User typed their own question instead of selecting a suggestion
      setSuggestionSelected(true);

      // Add user message
      setMessages((msgs) => [...msgs, { type: "user", text: trimmed }]);
      setInput("");

      // Transition to done state
      setOnboarding("done");

      // Send the message
      handleSendSuggestion(trimmed);
    } else if (onboarding === "done") {
      // Normal chat flow - add user message
      setMessages((msgs) => [...msgs, { type: "user", text: trimmed }]);
      setInput("");

      // Add loading indicator
      setMessages((prev) => [...prev, { type: "loading" }]);

      // If new customer with upload or product selection, increment free message count
      if (isNewCustomerWithUpload || isProductSelection) {
        if (!reachedMessageLimit) {
          setFreeMessageCount((count) => {
            const newCount = count + 1;
            if (newCount === MAX_FREE_MESSAGES) {
              setReachedMessageLimit(true);
            }
            return newCount;
          });
        } else {
          // Prompt for phone number, disable chat, and set onboarding
          setMessages((prev) => [
            ...prev.filter((msg) => msg.type !== "loading"),
            {
              type: "ai",
              text: "To continue chatting, please enter your phone number for unlimited access.",
            },
          ]);
          setRequireVerification(true);
          setOnboarding("new-customer-phone");
          return; // Do not send the message to the AI
        }
      }

      try {
        // Get policy ID from sessionStorage if available, but only for policy upload flows
        const policyId = !isProductSelection
          ? sessionStorage.getItem("policyId")
          : null;

        // Get variant ID from sessionStorage if available
        let variantIdToSend = currentVariantId;
        if (!variantIdToSend) {
          variantIdToSend = sessionStorage.getItem("variant_id") || undefined;
        }

        // Send query to AI chat service
        const response = await sendChatQueryMutation.mutateAsync({
          query: trimmed,
          session_id: sessionId,
          variant_id: variantIdToSend,
          agent_type: "function",
          policy_id: policyId || undefined,
        });

        // Update session ID for future requests
        if (response.payload.session_id) {
          setSessionId(response.payload.session_id);
        }

        // Remove loading indicator and add AI response as markdown
        setMessages((prev) => [
          ...prev.filter((msg) => msg.type !== "loading"),
          {
            type: "ai-markdown",
            text: response.payload.response,
          },
        ]);
      } catch (error) {
        // Remove loading indicator and add error message with Calendly fallback
        const errorMessage =
          error instanceof Error
            ? `Sorry, I encountered an error: ${error.message}`
            : "Sorry, I couldn't process your request. Please try again.";

        const errorWithFallback = `${errorMessage}\n\nIf you continue to experience issues, you can [book a call with our team](https://calendly.com/oneassure/30min) for personalized assistance.`;

        setMessages((prev) => [
          ...prev.filter((msg) => msg.type !== "loading"),
          {
            type: "ai-markdown",
            text: errorWithFallback,
          },
        ]);
      }
    } else {
      // Fallback for any other state
      setMessages((msgs) => [...msgs, { type: "user", text: trimmed }]);
      setInput("");

      // Add loading message and response immediately
      setMessages((prev) => [...prev, { type: "loading" }]);

      setMessages((msgs) => [
        ...msgs.filter((msg) => msg.type !== "loading"),
        {
          type: "ai",
          text: "I'm your OneAssure assistant. How can I help you further?",
        },
      ]);
    }
  }

  // Modify the handleResendOtp function to integrate with the chat flow
  const handleResendOtp = () => {
    if (!phone) {
      return;
    }

    // Add user message for resend request
    setMessages((msgs) => [
      ...msgs,
      {
        type: "ai",
        text: "Sending a new verification code to your phone...",
      },
    ]);

    // Show loading indicator
    setMessages((prev) => [...prev, { type: "loading" }]);

    // Call the initiate verification API again
    initiateVerificationMutation.mutate(
      { phone_number: phone, scopes: ["CHAT"] },
      {
        onSuccess: (response) => {
          // Store new OTP token
          if (response.payload?.otpToken) {
            setOtpToken(response.payload.otpToken);
          }

          const countdownKey = `otp-resend-${Date.now()}`;

          // Update UI with integrated countdown
          setMessages((msgs) => [
            ...msgs.filter((msg) => msg.type !== "loading"),
            {
              type: "ai",
              text: `I've sent a new 6-digit verification code to your phone number ending in ${phone.slice(
                -4
              )}. Please enter that code below:`,
              component: (
                <OtpCountdown
                  key={countdownKey}
                  onResend={handleResendOtp}
                  phoneLastDigits={phone.slice(-4)}
                />
              ),
            },
          ]);
        },
        onError: (error) => {
          const otpErrorMessage =
            error instanceof Error
              ? error.message
              : "I couldn't send a new verification code. Please try again in a few minutes.";

          const otpErrorWithFallback = `${otpErrorMessage}\n\nIf you continue to have trouble with verification, you can [book a call with our team](https://calendly.com/oneassure/30min) for immediate assistance.`;

          setMessages((msgs) => [
            ...msgs.filter((msg) => msg.type !== "loading"),
            {
              type: "ai-markdown",
              text: otpErrorWithFallback,
            },
          ]);
        },
      }
    );
  };

  const handleNewChat = () => {
    // Reset all conversation state
    setMessages([{ type: "loading" }]);
    setInput("");
    setOnboarding("select");
    setPhone("");
    setOtpToken("");
    setPolicyFile(null);
    setSelectedPolicy(null);
    setSessionId(null);
    setCurrentVariantId(undefined);
    setFreeMessageCount(0);
    setIsNewCustomerWithUpload(false);
    setRequireVerification(false);
    setReachedMessageLimit(false);
    setIsProductSelection(false);
    setProductSelected(false);
    setSelectedProductInfo(null);
    setSuggestionSelected(false);
    setShowSuccessToast(false);
    setCustomerSelected(false);
    setProductSelectionCompleted(false);
    setPolicyDocumentChoiceMade(false);
    setPolicySelectionCompleted(false);

    // Clear session storage
    sessionStorage.removeItem("policyId");
    sessionStorage.removeItem("variant_id");
    sessionStorage.removeItem("selectedPolicyId");
    sessionStorage.removeItem("accessToken");

    // Restart with welcome flow immediately
    setMessages([
      { type: "ai", text: "Welcome to OneAssure! How can I help you today?" },
      { type: "select-customer" },
    ]);
  };

  // Helper to check if loading is present (awaiting response)
  const isSuggestionsDisabled = messages.some((msg) => msg.type === "loading");

  return (
    <div
      className="flex flex-col h-[70vh] md:h-[85vh] max-w-4xl mx-auto relative overflow-hidden my-2"
      style={{ position: "relative" }}
    >
      {/* Background decoration */}

      {/* Chat Interface */}
      <div
        className="flex flex-col h-full z-10 bg-white shadow-xl rounded-xl overflow-hidden border border-gray-200 my-4 mx-4 min-h-0"
        style={{
          overscrollBehavior: "contain",
          isolation: "isolate",
        }}
      >
        <ChatHeader onNewChat={handleNewChat} />
        <ChatMessages
          messages={messages}
          chatAreaRef={chatAreaRef}
          onSelect={handleSelect}
          onUploadComplete={handlePolicyUpload}
          onPolicySelected={handlePolicySelected}
          onVerifyPhone={handleStartVerification}
          onPolicyDocumentChoice={handlePolicyDocumentChoice}
          onProductSelected={handleProductSelected}
          isProductSelected={productSelected}
          onSuggestionClick={handleSuggestionClick}
          isSuggestionSelected={false}
          isSuggestionsDisabled={isSuggestionsDisabled}
          suggestionsContext={isProductSelection ? "product" : "policy"}
          customerSelected={customerSelected}
          productSelectionCompleted={productSelectionCompleted}
          policyDocumentChoiceMade={policyDocumentChoiceMade}
          policySelectionCompleted={policySelectionCompleted}
        />
        <ChatInput
          input={input}
          setInput={setInput}
          onboarding={onboarding}
          handleSend={handleSend}
          isDisabled={
            isSuggestionsDisabled ||
            (requireVerification && onboarding === "done") ||
            onboarding === "upload-error" ||
            onboarding === "new-policy-choice" ||
            onboarding === "select" ||
            onboarding === "existing-policy" ||
            onboarding === "new-upload" ||
            onboarding === "new-catalog"
          }
        />
      </div>

      {/* Success Toast */}
      {showSuccessToast && (
        <div className="fixed top-4 right-4 bg-white border border-green-100 text-gray-700 px-4 py-2 rounded-md shadow-sm z-50 flex items-center gap-2 animate-fadeIn">
          <svg
            className="w-4 h-4 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M5 13l4 4L19 7"
            />
          </svg>
          <span className="text-sm font-medium">Verification complete</span>
        </div>
      )}
    </div>
  );
}

// 6364334343
