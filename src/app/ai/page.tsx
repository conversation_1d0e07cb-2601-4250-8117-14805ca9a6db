import React from "react";
import Link from "next/link";

// Subtle blurred SVG backgrounds for visual interest

export default function OneassureAssistantLanding() {
  return (
    <>
      <main className="relative min-h-screen flex flex-col items-center px-4 overflow-x-hidden">
        {/* Hero Section */}
        <section className="relative w-full max-w-2xl text-center mt-20 mb-14 z-10">
          <h1 className="text-4xl sm:text-5xl font-bold mb-5 tracking-tight leading-tight">
            <span className="text-primary-blue-3">
              Unlock Insurance Secrets
            </span>{" "}
            Instantly with Oneassure AI!
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 mb-8 max-w-xl mx-auto">
            Don&apos;t miss out: Upload your policy and get jaw-dropping,
            expert-level answers in seconds. Chat 24/7 with the smartest
            insurance AI—absolutely free!
          </p>
          <Link href="/ai/chat" passHref legacyBehavior>
            <a className="mt-2 px-10 py-4 rounded-xl bg-gradient-to-r from-primary-green-2 to-primary-blue-3 text-white font-semibold text-lg shadow-lg inline-block text-center focus:outline-none focus:ring-2 focus:ring-primary-blue-3 focus:ring-offset-2 transition-colors">
              🚀 Try the AI Assistant Now
            </a>
          </Link>
        </section>

        {/* Features Section */}
        <section className="relative w-full max-w-4xl grid grid-cols-1 sm:grid-cols-3 gap-8 mb-20 z-10">
          <div className="flex flex-col items-center">
            <div className="mb-3 text-primary-green-2">
              <svg width="32" height="32" fill="none" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M12 2a10 10 0 100 20 10 10 0 000-20zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-lg mb-2 text-white">
              Lightning-Fast Policy Insights
            </h3>
            <p className="text-gray-800 text-sm text-center">
              Upload your policy and get mind-blowing, actionable advice in
              seconds—no more waiting, no more confusion!
            </p>
          </div>
          <div className="flex flex-col items-center">
            <div className="mb-3 text-primary-blue-3">
              <svg width="32" height="32" fill="none" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M12 4a8 8 0 100 16 8 8 0 000-16zm1 11h-2v-2h2v2zm0-4h-2V7h2v4z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-lg mb-2 text-white">
              24/7 AI Chat—No Human Limits
            </h3>
            <p className="text-gray-800 text-sm text-center">
              Ask anything, anytime. Get expert answers, personalized tips, and
              insurance hacks—day or night!
            </p>
          </div>
          <div className="flex flex-col items-center">
            <div className="mb-3 text-primary-green-2">
              <svg width="32" height="32" fill="none" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-lg mb-2 text-white">
              Rock-Solid Privacy & Security
            </h3>
            <p className="text-gray-800 text-sm text-center">
              Your secrets are safe! We use cutting-edge encryption—your data
              stays private, always.
            </p>
          </div>
        </section>

        {/* How it Works Section */}
        <section className="relative w-full max-w-2xl mb-24 z-10">
          <h2 className="text-2xl font-bold mb-7 text-center">
            How to Get Instant Insurance Superpowers
          </h2>
          <ol className="space-y-7 text-gray-600">
            <li className="flex items-center gap-4">
              <span className="flex-shrink-0 w-9 h-9 flex items-center justify-center rounded-full bg-primary-green-2 text-black font-bold text-lg">
                1
              </span>
              <span className="text-base sm:text-lg leading-snug">
                Smash <b>Try the AI Assistant Now</b> and breeze through phone
                verification.
              </span>
            </li>
            <li className="flex items-center gap-4">
              <span className="flex-shrink-0 w-9 h-9 flex items-center justify-center rounded-full bg-primary-blue-3 text-black font-bold text-lg">
                2
              </span>
              <span className="text-base sm:text-lg leading-snug">
                Upload your insurance PDF or pick a policy—no paperwork, no
                hassle.
              </span>
            </li>
            <li className="flex items-center gap-4">
              <span className="flex-shrink-0 w-9 h-9 flex items-center justify-center rounded-full bg-primary-green-2 text-black font-bold text-lg">
                3
              </span>
              <span className="text-base sm:text-lg leading-snug">
                Start chatting and unlock answers, tips, and savings you never
                knew existed!
              </span>
            </li>
          </ol>
        </section>
      </main>
    </>
  );
}
