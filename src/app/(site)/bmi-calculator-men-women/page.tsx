import BMI from "@/components/Tools/BMI";
import axios from "axios";
import {
  dummyHowToCalculate,
  dummyBMIExampleData,
} from "@/components/Tools/BMI/dto/dtos";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import FAQSchema from "@/components/SchemaMarkup/FaqSchema";
import { Suspense } from "react";
import { PageSchema } from "@/components/SchemaMarkup/PageSchema";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import { fetchAllInsurers } from "@/scripts/fetchAllInsurers";

// Configure axios with timeout and retry logic
const apiClient = axios.create({
  timeout: 10000, // 10 second timeout
  headers: {
    "Content-Type": "application/json",
  },
});

type BMIBasedHealthInsuranceProduct = {
  id: string;
  title: string;
  points: string[];
};

type BMIFAQ = {
  id: string;
  question: string;
  answer: string;
};

type BMIBelowHealthyAboveRange = {
  id: string;
  title: string;
  subtitle: string;
  bmi_below_healthy_above_points: string[];
};

type BMIForMenWomenChildren = {
  id: string;
  title: string;
  content: string;
  bmi_below_healthy_above_ranges: BMIBelowHealthyAboveRange[];
};

type BMITestimonial = {
  id: string;
  name: string;
  content: string;
};

type BMIContent = {
  id: string;
  title: string;
  content: string;
  bmi_based_health_insurance_products: BMIBasedHealthInsuranceProduct[];
  bmi_faqs: BMIFAQ[];
  bmi_for_men_women_children: BMIForMenWomenChildren[];
  bmi_related_insurance_plans: any[];
  bmi_testimonials: BMITestimonial[];
};

type ApiResponse = {
  payload: {
    data: {
      site_bmi_content: BMIContent;
    };
  };
  metadata: null;
};

export const dynamic = "force-static";
export const dynamicParams = false;
export const revalidate = false;

export async function generateStaticParams() {
  // Since this is a single page without dynamic routes, return empty array
  return [];
}

// Add request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

const title =
  "BMI Calculator - Calculate Body Mass Index of Women, Men, & Kids Online | Oneassure";
const description =
  "Use our free BMI calculator for men and women to quickly check your Body Mass Index. Discover whether you're underweight, normal, overweight, or obese based on your height and weight — only at Oneassure.in.";
const imageUrl =
  "https://cdn.oasr.in/oa-site/cms-uploads/media/Clip_path_group_beb53ec1ee.svg";
const pageUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/bmi-calculator-men-women`;
const pageContent =
  "BMI Calculator: Use our free BMI calculator for men and women to quickly check your Body Mass Index. Discover whether you're underweight, normal, overweight, or obese based on your height and weight - only at Oneassure.in. BMI Chart: A BMI calculator helps insurers assess your health risk. Higher or lower BMI can affect premium rates, eligibility, and medical underwriting. Maintaining a healthy BMI improves chances of getting better insurance terms, lower premiums, and reduces risk-based exclusions during policy approval. Scientific Formula: Based on globally accepted medical standards. Insurance Ready: Directly applicable for health insurance applications. Why use our BMI Calculator? OneAssures BMI Calculator helps you understand your Body Mass Index so you can take informed steps towards better health and insurance planning. Instant & Accurate Results - Just enter height and weight, get your BMI instantly. Health Risk Awareness - Know if you are underweight, healthy, overweight, or obese. Insurance Preparedness - Use BMI insights to choose the right health cover for your profile. Customer Testimonials: When Niva Bupa unfairly rejected my claim, OneAssure stood by me - guiding paperwork, deadlines, and hearings. Their expert, constant support turned confusion into confidence. Thanks to them, I won my case and got what was rightfully mine - Pooja Raikwar. BMI based Health Insurance Products: Get a clear side-by-side view of leading health insurance plans - compare coverage, benefits, and premiums based on your needs and BMI. Individual Health Plan: Personalised coverage just for you, affordable premiums for solo protection, covers hospitalisation & medical bills, ideal for working professionals & singles. Family Health Plan: One policy for your entire family, shared sum insured for all members, covers spouse, kids & parents together, cost-effective with wider protection. Senior Citizen Plan: Specialised plans for 60+ age group, higher coverage for age-related illnesses, covers pre-existing conditions, peace of mind for your golden years. How to calculate Body Mass Index: Check Weight - Weigh yourself accurately. Measure Height - Note your exact height. Fill Details - Input weight and height. Get Results - See which range you fall in. Standard BMI Formula: This simple formula calculates your BMI by dividing your weight in kilograms by the square of your height in meters. BMI (Body Mass Index) = Weight (kg) ÷ [Height (m) × Height (m)]. Example: Age 28 years, Weight 70 kg, Height 175 cm, BMI: 22.9 (Normal range). BMI for Men: Healthy BMI 18.5 to 24.9 reduces risk of heart disease, diabetes, hypertension. Below 18.5 causes nutritional deficiency, osteoporosis, anemia, weak immunity, hormonal imbalance. 18.5 to24.9 supports heart health, diabetes prevention, normal blood pressure. Above 25 increases risk of coronary artery disease, diabetes, hypertension, osteoarthritis, fatty liver. BMI for Women: Healthy BMI 18.5 to 24.9 supports reproductive health. Below 18.5 increases risk of amenorrhea, osteoporosis, infertility, anemia, weak immunity. 18.5 to 24.9 lowers risks of PCOS, diabetes, heart disease, hypertension, stroke. Above 25 increases risk of PCOS, diabetes, heart disease, hypertension, endometrial cancer. BMI for Kids: Healthy BMI varies with age and gender, supporting growth and energy. Below healthy range causes poor nutrition, low stamina, weak immunity, delayed growth. Above range increases risk of obesity, diabetes, high BP, joint strain, long-term metabolic issues. Related Insurance Plans: Child Health Plan covers kids expenses, vaccinations, routine check-ups, protection against future risks. Family Floater Plan offers one plan for whole family, shared coverage, lower premium, comprehensive protection. Disease Specific Plan provides targeted cover for specific illnesses, long-term treatment support, reduced financial burden. Frequently Asked Questions: 1. BMI is a number using height and weight to assess health. 2. BMI identifies underweight, healthy, overweight, or obese ranges. 3. Calculate by formula or online tool. 4. General screening tool for adults; kids use age/gender charts. 5. Healthy range is 18.5 to 24.9. 6. During pregnancy, BMI is not used, weight is tracked separately. 7. Formula is same for men and women, but interpretation may differ. 8. BMI does not affect claim approval but affects premiums. 9. BMI impacts health insurance premium risk assessment. 10. OneAssures BMI Calculator helps choose right plan. 11. Overweight increases risks of heart disease, diabetes, hypertension, joint issues, cancer. 12. Maintain BMI with diet, exercise, sleep, hydration. 13. BMI in death clock estimates life expectancy based on weight.";

export async function generateMetadata() {
  const keywords = [
    "BMI Calculator",
    "body mass index calculator",
    "bmi calculator female",
    "bmi calculator Women",
    "bmi calculator Men",
    "bmi calculator male",
    "bmi calculator kid",
    "BMI Calculator Online",
  ];

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: "website",
      url: pageUrl,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      siteName: "OneAssure",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [imageUrl],
      domain: "oneassure.in",
      url: pageUrl,
    },
    metadataBase: new URL("https://www.oneassure.in"),
    alternates: {
      canonical: `/bmi-calculator-men-women`,
    },
    other: {
      // Facebook Meta Tags
      "og:url": pageUrl,
      "og:type": "website",
      "og:title": title,
      "og:description": description,
      "og:image": imageUrl,

      // Twitter Meta Tags
      "twitter:card": "summary_large_image",
      "twitter:domain": "oneassure.in",
      "twitter:url": pageUrl,
      "twitter:title": title,
      "twitter:description": description,
      "twitter:image": imageUrl,
    },
  };
}

async function getBMIWomenMenData(): Promise<BMIContent[]> {
  try {
    const query = `query MyQuery {
  site_bmi_content {
    id
    title
    content
    bmi_based_health_insurance_products {
      id
      title
      points
    }
    bmi_faqs {
      id
      question
      answer
    }
    bmi_for_men_women_children {
      id
      title
      content
      bmi_below_healthy_above_ranges {
        id
        title
        subtitle
        bmi_below_healthy_above_points {
          id
          point_title
          point_content
        }
      }
    }
    bmi_related_insurance_plans {
      id
      title
      points
    }
    bmi_testimonials {
      id
      name
      content
    }
  }
}`;
    const operationName = "MyQuery";
    const variables = {};
    const response = await fetchStudioCmsData(query, operationName, variables);

    // Check if response has the expected structure
    if (response.payload.data) {
      return response.payload.data.site_bmi_content;
    } else {
      return [];
    }
  } catch (error) {
    return [];
  }
}

// Add data transformation function
function transformBMIData(apiData: any) {
  if (!apiData || !Array.isArray(apiData)) return [{ plans: [] }];

  const plans = apiData.map((item) => ({
    title: item.title,
    description: item.content,
    iconKey: item.title.toLowerCase().includes("men")
      ? "men"
      : item.title.toLowerCase().includes("women")
      ? "women"
      : "children",
    iconBgColor: item.title.toLowerCase().includes("men")
      ? "#3B82F6"
      : item.title.toLowerCase().includes("women")
      ? "#EC4899"
      : "#10B981",
    button: "Learn More",
    features:
      item.bmi_below_healthy_above_ranges?.map((range: any) => range.title) ||
      [],
    bmiRanges:
      item.bmi_below_healthy_above_ranges?.map((range: any) => ({
        title: range.title,
        range: range.subtitle,
        description: range.subtitle,
        risks:
          range.bmi_below_healthy_above_points?.map((point: any) => ({
            title: point.point_title,
            description: point.point_content,
          })) || [],
        expanded: false,
      })) || [],
  }));

  return [{ plans }];
}

const BMIWomenMen = async () => {

  async function getBlogData() {
    try {
      // Check if environment variables are available
      if (!process.env.STRAPI_BASEURL || !process.env.STRAPI_TOKEN) {
        return {
          heading: "Latest Blog Posts",
          blogs: [],
        };
      }

      const headers = {
        Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
      };

      const res = await fetch(
        `${process.env.STRAPI_BASEURL}/api/blogs?filters[topBlog][$eq]=true&populate[Thumbnail][fields][0]=url&populate[category][fields][0]=name&populate[category][fields][1]=slug&populate[author][fields][0]=name&fields[0]=title&fields[1]=subtitle&fields[2]=createdAt&fields[3]=slug&populate[subCategory][fields][0]=name&populate[subCategory][fields][1]=slug&pagination[pageSize]=6&pagination[page]=1&sort[0]=createdAt:desc`,
        {
          headers,
        }
      );

      if (!res.ok) {
        return {
          heading: "Latest Blog Posts",
          blogs: [],
        };
      }

      const data = await res.json();

      // Enhanced data transformation with error handling
      const transformedData = {
        heading: "Latest Blog Posts",
        blogs: data.data
          .map((blog: any, index: number) => {
            try {
              return {
                title: blog.attributes?.Title || `Blog Post ${index + 1}`,
                date: blog.attributes?.createdAt
                  ? new Date(blog.attributes.createdAt).toLocaleDateString(
                      "en-US",
                      {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      }
                    )
                  : "Recent",
                author:
                  blog.attributes?.author?.data?.attributes?.name ||
                  "OneAssure Team",
                description: blog.attributes?.subtitle || "",
                imageUrl:
                  blog.attributes?.Thumbnail?.data?.attributes?.url || "",
                url: `/insurance/${
                  blog.attributes?.category?.data?.attributes?.slug || "blog"
                }/${blog.attributes?.slug || ""}`,
              };
            } catch (error) {
              return null;
            }
          })
          .filter(Boolean), // Limit to 6 blogs and remove null items
      };

      return transformedData;
    } catch (error) {
      // Return fallback data instead of throwing
      return {
        heading: "Latest Blog Posts",
        blogs: [],
      };
    }
  }

  const bmiWomenMenData = await getBMIWomenMenData();
  // Fetch insurer data
  let allInsurerData = [];
  try {
    allInsurerData = await fetchAllInsurers();
  } catch (error) {
    allInsurerData = [];
  }

  // Fetch blog data
  let blogData = { heading: "Latest Blog Posts", blogs: [] };
  try {
    blogData = await getBlogData();
  } catch (error) {
    blogData = { heading: "Latest Blog Posts", blogs: [] };
  }

  const breadcrumbs = [
    { name: "Oneassure", item: "https://www.oneassure.in/" },
    {
      name: "BMI Calculator for Men and Women",
      item: "https://www.oneassure.in/bmi-calculator-men-women",
    },
  ];

  return (
    <>
      <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
      <FAQSchema faqs={bmiWomenMenData[0]?.bmi_faqs} />
      <PageSchema
        name="BMI Calculator"
        url={pageUrl}
        headline={title}
        description={description}
        mainContentOfPage={pageContent}
        inLanguage="en"
        mainEntity={{
          name: "BMI Calculator",
          type: "WebPage",
        }}
        publisher={{
          name: "Oneassure",
          logoUrl: imageUrl,
        }}
        image={{
          contentUrl: imageUrl,
        }}
        datePublished={new Date().toISOString()}
        dateModified={new Date().toISOString()}
      />
      <Suspense fallback={<div>Loading...</div>}>
        <BMI
          allInsurerData={allInsurerData}
          blogData={blogData}
          howToCalculate={dummyHowToCalculate}
          exampleData={dummyBMIExampleData}
          consumerData={transformBMIData(
            bmiWomenMenData[0]?.bmi_for_men_women_children
          )}
          testimonials={bmiWomenMenData[0]?.bmi_testimonials}
          faqs={bmiWomenMenData[0]?.bmi_faqs}
        />
      </Suspense>
    </>
  );
};

export default BMIWomenMen;
