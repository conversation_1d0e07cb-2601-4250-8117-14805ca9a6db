import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import SuperTopUp from "@/components/SuperTopUp";
import React from "react";
import transformSuperTopUpData from "@/components/SuperTopUp/dto";
import { SuperTopUpApiResponse } from "@/components/SuperTopUp/types";
import { sampleSuperTopUpData } from '@/components/SuperTopUp/data';
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import { PageSchema } from "@/components/SchemaMarkup/PageSchema";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";
import TableOfContentSchema from "@/components/SchemaMarkup/TableOfContentSchema";
import axios from "axios";
import { fetchAllInsurers } from "@/scripts/fetchAllInsurers";

export async function generateMetadata({ params }: { params: {slug: string; id: string } }) {
  try {
    const query = `
      query MyQuery($id:String!){
        site_super_top_up_page(where:(id:{_eq:$id})){
          id
          hero_image_url
          health_insurer {
            id
            name
            slug
          }
          super_top_up_page_seo {
            id
            meta_title
            meta_description
            keywords
            canonical
          }
        }         
    }`;

    const operationName = "MyQuery";
    const variables = { id: params.id };
    const response = await fetchStudioCmsData(query, operationName, variables);
    const pageData = response.payload.data?.site_super_top_up_page?.[0];

    if (!pageData) {
      throw new Error("Page data not found");
    }
    const seoData = pageData.super_top_up_page_seo;

    const title = seoData?.meta_title || `${pageData.health_insurer.name} Super-Top-Up Plans - Check ${pageData.health_insurer.name} Health Insurance Topup benefits & Buy Online| Oneassure`;
    const description = seoData?.meta_description || `Boost your health coverage with ${pageData.health_insurer.name} Top-Up & Super Top Up Plans—affordable protection against high medical bills, lifetime renewability, and coverage at Oneassure.in.`;
    const canonical = seoData?.canonical || `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}`;
    const keywords = seoData?.keywords || [
      `${pageData.health_insurer.name} super top up`,
      `${pageData.health_insurer.name} health insurance super top up`,
      `${pageData.health_insurer.name} super top up plan`,
      `${pageData.health_insurer.name} super top up Benefits`,
      `${pageData.health_insurer.name} super top up eligibility`,
      `${pageData.health_insurer.name} health insurance topup `,
    ];
    const imageUrl = pageData.hero_image_url || "";
    return {
      title: title,
      description: description,
      keywords: keywords,
      openGraph: {
        title: title,
        description: description,
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: title ,
        description: description,
        images: imageUrl? [imageUrl] : [],
         domain: "oneassure.in",
        url: canonical,
      },
      metadataBase: new URL(
        process.env.NEXT_PUBLIC_BASE_URL || "https://www.oneassure.in"
      ),
      alternates: {
        canonical: canonical,
      },
      other: {
        // Facebook Meta Tags
        "og:url": canonical,
        "og:type": "website",
        "og:title": title,
        "og:description": description,
        "og:image": imageUrl,

        // Twitter Meta Tags
        "twitter:card": "summary_large_image",
        "twitter:domain": "oneassure.in",
        "twitter:url": canonical,
        "twitter:title": title,
        "twitter:description": description,
        "twitter:image": imageUrl,
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Niva Bupa Super-Top-Up Plans - Check Niva Bupa Health Insurance Topup benefits & Buy Online| Oneassure",
      description:"Boost your health coverage with Niva Bupa Top-Up & Super Top Up Plans—affordable protection against high medical bills, lifetime renewability, and coverage at Oneassure.in.",
      keywords:"Niva Bupa super top up, Niva Bupa health insurance super top up, Niva Bupa super top up plan, Niva Bupa super top up Benefits, Niva Bupa super top up eligibility, Niva Bupa health insurance topup ",
      canonical: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}`,
      openGraph: {
        title: "Niva Bupa Super-Top-Up Plans - Check Niva Bupa Health Insurance Topup benefits & Buy Online| Oneassure",
        description:
          "Boost your health coverage with Niva Bupa Top-Up & Super Top Up Plans—affordable protection against high medical bills, lifetime renewability, and coverage at Oneassure.in.",
        type: "website",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}`,
      },
      metadataBase: new URL(
        process.env.NEXT_PUBLIC_BASE_URL || "https://www.oneassure.in"
      ),
      alternates: {
        canonical: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}`,
      },
      other: { 
        // Facebook Meta Tags
        "og:url": `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}`,
        "og:type": "website",
        "og:title": "Niva Bupa Super-Top-Up Plans - Check Niva Bupa Health Insurance Topup benefits & Buy Online| Oneassure",
        "og:description":
          "Boost your health coverage with Niva Bupa Top-Up & Super Top Up Plans—affordable protection against high medical bills, lifetime renewability, and coverage at Oneassure.in.",
        "og:image": "",

        // Twitter Meta Tags
        "twitter:card": "summary_large_image",
        "twitter:domain": "oneassure.in",
        "twitter:url": `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}`,
        "twitter:title": "Niva Bupa Super-Top-Up Plans - Check Niva Bupa Health Insurance Topup benefits & Buy Online| Oneassure",
        "twitter:description":
          "Boost your health coverage with Niva Bupa Top-Up & Super Top Up Plans—affordable protection against high medical bills, lifetime renewability, and coverage at Oneassure.in.",
        "twitter:image": "",
      },
    };
  }
}

async function getPageData(id: string): Promise<SuperTopUpApiResponse> {
  const query = `
    query MyQuery($id: String!) {
      site_super_top_up_page(where:{id:{_eq:$id}}) {
        id
        pill_content
        hero_title
        hero_description
        hero_image_url
        health_insurer {
          id
          name
          slug
        }
        super_top_up_page_claim_settlement_section {
          id
          pill_content
          section_title
          section_description
          super_top_up_page_claim_settlement_types {
            id
            title
            type
            super_top_up_page_claim_settlement_steps {
              id
              title
              description
            }
          }
        }
        super_top_up_page_faq_section {
          id
          pill_content
          section_title
          section_description
          super_top_up_page_faq_section_points {
            id
            question
            answer
          }
        }
        super_top_up_page_hero_cards {
          id
          title
          description
          icon_url
        }
        super_top_up_page_how_to_buy_section {
          id
          pill_content
          section_title
          section_description
          super_top_up_page_how_to_buy_steps {
            id
            title
            description
          }
        }
        super_top_up_page_inclusion_section {
          id
          pill_content
          section_title
          section_description
          super_top_up_page_inclusion_section_points {
            id
            type
            points
          }
        }
        super_top_up_page_insurance_category_section {
          id
          pill_content
          section_title
          section_description
          super_top_up_page_insurance_category_section_cards {
            id
            title
            points
            most_popular
            icon_url
          }
        }
        super_top_up_page_renewal_section {
          id
          pill_content
          section_title
          section_description
          super_top_up_page_renewal_types {
            id
            type
            title
            super_top_up_page_renewal_steps {
              id
              title
              description
            }
          }
        }
        super_top_up_page_testimonial_section {
          id
          pill_content
          section_title
          section_description
          super_top_up_page_testimonial_section_points {
            id
            name
            description
          }
        }
        super_top_up_page_verdicts {
          id
          pill_content
          title
          verdict
          super_top_up_page_verdict_pros_cons {
            id
            title
            type
            points
          }
        }
        super_top_up_page_sections {
          id
          pill_content
          section
          section_title
          section_description
          super_top_up_page_section_cards {
            id
            title
            icon_url
            description
          }
        }
      }
    }
  `;

  const operationName = "MyQuery";
  const variables = { id: id };
  const response = await fetchStudioCmsData(query, operationName, variables);
  if(response.payload.data && response.payload.data.site_super_top_up_page.length > 0){
    return response.payload.data.site_super_top_up_page[0];
  }
  return sampleSuperTopUpData;
}


async function getBlogData() {
  try {
    // Check if environment variables are available
    if (
      !process.env.NEXT_PUBLIC_STRAPI_BASEURL ||
      !process.env.NEXT_PUBLIC_STRAPI_TOKEN
    ) {
      return {
        heading: "Latest Blog Posts",
        blogs: [],
      };
    }

    const headers = {
      Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
    };

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_STRAPI_BASEURL}/api/blogs?filters[topBlog][$eq]=true&populate[Thumbnail][fields][0]=url&populate[category][fields][0]=name&populate[category][fields][1]=slug&populate[author][fields][0]=name&fields[0]=title&fields[1]=subtitle&fields[2]=createdAt&fields[3]=slug&populate[subCategory][fields][0]=name&populate[subCategory][fields][1]=slug&pagination[pageSize]=100&pagination[page]=1&sort[0]=createdAt:desc`,
      {
        headers,
        next: { revalidate: 0 }, // Force fresh data
      }
    );

    if (!res.ok) {
      throw new Error(`Blog API responded with status: ${res.status}`);
    }

    const data = await res.json();

    // Enhanced data transformation with error handling
    const transformedData = {
      heading: "Latest Blog Posts",
      blogs: data.data
        .map((blog: any, index: number) => {
          try {
            return {
              title: blog.attributes?.Title || `Blog Post ${index + 1}`,
              date: blog.attributes?.createdAt
                ? new Date(blog.attributes.createdAt).toLocaleDateString(
                    "en-US",
                    {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    }
                  )
                : "Recent",
              author:
                blog.attributes?.author?.data?.attributes?.name ||
                "OneAssure Team",
              description: blog.attributes?.subtitle || "",
              imageUrl: blog.attributes?.Thumbnail?.data?.attributes?.url || "",
              url: `/insurance/${
                blog.attributes?.category?.data?.attributes?.slug || "blog"
              }/${blog.attributes?.slug || ""}`,
            };
          } catch (error) {
            return null;
          }
        })
        .filter(Boolean)
        .slice(0, 6), // Limit to 6 blogs and remove null items
    };

    return transformedData;
  } catch (error) {
    // Return fallback data instead of throwing
    return {
      heading: "Latest Blog Posts",
      blogs: [],
    };
  }
}


const page = async ({ params }: { params: { slug: string; id: string } }) => {
  const pageData = await getPageData(params.id);
  const transformedData = transformSuperTopUpData(pageData);

  if (!transformedData) {
    return null;
  }

  const breadcrumbs = [
    {
      name: "OneAssure",
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/`,
    },
    {
      name: "Health Insurance",
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance`,
    },
    {
      name: transformedData.healthInsurer.name,
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.healthInsurer.slug}-health-insurance/hi/${transformedData.healthInsurer.id}`,
    },
    {
      name: "Top UP Plan", 
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}`,
    },
  ];
  const pageSchema = {
    name: transformedData.heroSection.title,
    url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}`,
    headline: transformedData.heroSection.title,
    description: transformedData.heroSection.description,
  };

  const pageNavigationSection = {
    name: "Verdict",
    itemListElement: [
      {
        position: 1,
        name: "Verdict",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#expert-review`,
      },
      {
        position: 2,
        name: "About Super Top Up",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#about-super-top-up`,
      },
      {
        position: 3,
        name: "Testimonials",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#testimonials`,
      },  
      {
        position: 4,
        name: "Unique Features",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#unique-features`,
      },  
      {
        position: 5,
        name: "Factors To Consider",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#factors-to-consider`,
      },  
      {
        position: 6,
        name: "Inclusions and Exclusions",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#inclusions-and-exclusions`,
      },
      {
        position: 7,
        name: "Claim Settlement Process",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#claim-settlement`,
      },
      {
        position: 8,
        name: "Renewal Process",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#renewal-process`,
      },
      {
        position: 9,
        name: "FAQs",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/st/${params.id}#faqs`,
      }
    ],
  }; 
  
  const pageContent = transformedData.heroSection.description;
  const allInsurerData = await fetchAllInsurers();
  const blogData = await getBlogData();
  return (
    <>
      <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
      <PageSchema
        name={pageSchema.name}
        url={pageSchema.url}
        headline={pageSchema.headline}
        description={pageContent}
      />
      <FaqSchema faqs={transformedData.faqSection.faqs} />
      <TableOfContentSchema data={pageNavigationSection} />
      <SuperTopUp data={transformedData} allInsurerData={allInsurerData} blogData={blogData} />
    </>
  );
};
export default page;
