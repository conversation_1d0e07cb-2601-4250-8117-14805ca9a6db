import Insurer from "@/components/Insurer/index";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";
import TableOfContentSchema from "@/components/SchemaMarkup/TableOfContentSchema";
import { PageSchema } from "@/components/SchemaMarkup/PageSchema";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import {
  HealthInsurerApiResponse,
  HealthInsurer,
} from "@/components/Insurer/dto/dtos";
import axios from "axios";
import { fetchAllInsurers } from "@/scripts/fetchAllInsurers";

// Dynamic runtime configuration for API calls
export const dynamic = "force-dynamic";
export const dynamicParams = true;
export const revalidate = 0;
export const fetchCache = "force-no-store";

// Generating metadata for the page
export async function generateMetadata({ params }: { params: { id: string } }) {
  const { id } = params;

  let name: string;
  let slug: string;
  let logoUrl: string;

  try {
    // Fetch insurer data to get SEO information
    const { healthInsurer: insurerData } = await getInsurerData(id);

    name = insurerData.name;
    slug = insurerData.slug;
    logoUrl = insurerData.logo_url;

    // Use SEO data from API if available, otherwise fall back to generated values
    const seoData = insurerData.health_insurer_seo;
    const staticContent = insurerData.health_insurer_static_content;

    // Meta Data Variables - prioritize API data
    const title =
      seoData?.meta_title ||
      `${insurerData.name} Health Insurance - Check Reviews & Buy Online @Best Price | OneAssure`;

    const description =
      seoData?.meta_description ||
      `Buy ${insurerData.name} Health Insurance Online in India with cashless treatment. Find Claim Settlement Ratio, Network Hospitals, Documentation details, & Rating at Oneassure.in`;

    const pageUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/${insurerData.slug}/hi/${id}`;

    // Use logo from API, fallback to default image
    const imageUrl = logoUrl;

    // Generate keywords from API data and SEO meta_keyword
    const keywords = seoData?.meta_keyword
      ? seoData.meta_keyword.split(",").map((k) => k.trim())
      : [
          `${name} health insurance`,
          `${name} health insurance review`,
          `${name} health insurance online`,
          `buy ${name} health insurance`,
        ];

    return {
      title,
      description,
      keywords,
      openGraph: {
        title,
        description,
        type: "website",
        url: pageUrl,
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
        siteName: "OneAssure",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: [imageUrl],
        domain: "oneassure.in",
        url: pageUrl,
      },
      metadataBase: new URL(
        process.env.NEXT_PUBLIC_BASE_URL || "https://www.oneassure.in"
      ),
      alternates: {
        canonical: `/${slug}-health-insurance/hi/${id}`,
      },
      other: {
        // Facebook Meta Tags
        "og:url": pageUrl,
        "og:type": "website",
        "og:title": title,
        "og:description": description,
        "og:image": imageUrl,

        // Twitter Meta Tags
        "twitter:card": "summary_large_image",
        "twitter:domain": "oneassure.in",
        "twitter:url": pageUrl,
        "twitter:title": title,
        "twitter:description": description,
        "twitter:image": imageUrl,
      },
      // Handle prevent_indexing flag from SEO data
      ...(seoData?.prevent_indexing && {
        robots: {
          index: false,
          follow: false,
        },
      }),
    };
  } catch (error) {
    console.error("Error fetching metadata:", error);

  }
}

async function getInsurerData(id: string) {
  const query = `query MyQuery($id: String!) {
  health_insurers(where: {id: {_eq: $id}}) {
    id
    logo_url
    name
    slug
    temp_slug
    renewal_redirection_url
    renewal_integration_window
    renewal_integration_type
    preferred
    network_hospital_url
    claim_settlement_ratio
    solvency
    icr
    growth
    aum
    health_insurer_claim_settlements {
      id
      title
      health_insurer_claim_settlement_types {
        id
        title
        type
        health_insurer_claim_settlement_steps {
          id
          title
          description
          sequence
        }
      }
    }
    health_insurer_expert_reviews {
      id
      title
      description
      improvement_areas
      what_we_like
    }
    health_insurer_faqs {
      id
      question
      answer
      sequence
    }
    health_insurer_insurance_types {
      id
      title
      description
      button_text
    }
    health_insurer_network_hospital_detail {
      id
      network_hospital_count
      states_and_ut
      cities_covered
    }
    health_insurer_plans {
      id
      plan_type
      title
      points
      description
      health_product_variant {
        id
        variant_name
        variant_slug
        temp_slug
      }
    }
    health_insurer_policy_guides {
      id
      title
      health_insurer_policy_guide_points {
        id
        title
        description
        sequence
      }
    }
    health_insurer_pros_cons {
      id
      title
      type
      description
    }
    health_insurer_renewal_steps {
      id
      title
      health_insurer_renewal_types {
        id
        title
        type
        health_insurer_renewal_type_steps {
          id
          title
          description
          sequence
        }
      }
    }
    health_insurer_seo {
      id
      meta_description
      meta_keyword
      meta_title
      prevent_indexing
      source
    }
    health_insurer_static_content {
      id
      kyc_docs
      legacy
      renewal_key_points
      verdict
      hero_title
      hero_description
      customer_support_number
      customer_support_email
    }
    health_insurer_statistics {
      id
      industry
      type
      description
      company
    }
    health_insurer_testimonials {
      id
      name
      content
    }
    health_insurer_documents {
      id
      title
      required_documents
    }
  }
  site_health_insurer_ratings {
    id
    max_rating
    max_value
    min_rating
    min_value
    type
  }
}
`;
  const operationName = "MyQuery";
  const variables = {
    id: id,
  };
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BASE_URL}/api/studio-cms`,
    {
      query,
      operationName,
      variables,
    }
  );
  return {
    healthInsurer: response.data.payload.data.health_insurers[0] as HealthInsurer,
    siteHealthInsurerRatings: response.data.payload.data.site_health_insurer_ratings
  };
}

async function getBlogData() {
  try {
    // Check if environment variables are available
    if (
      !process.env.NEXT_PUBLIC_STRAPI_BASEURL ||
      !process.env.NEXT_PUBLIC_STRAPI_TOKEN
    ) {
      return {
        heading: "Latest Blog Posts",
        blogs: [],
      };
    }

    const headers = {
      Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
    };

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_STRAPI_BASEURL}/api/blogs?filters[topBlog][$eq]=true&populate[Thumbnail][fields][0]=url&populate[category][fields][0]=name&populate[category][fields][1]=slug&populate[author][fields][0]=name&fields[0]=title&fields[1]=subtitle&fields[2]=createdAt&fields[3]=slug&populate[subCategory][fields][0]=name&populate[subCategory][fields][1]=slug&pagination[pageSize]=100&pagination[page]=1&sort[0]=createdAt:desc`,
      {
        headers,
        next: { revalidate: 0 }, // Force fresh data
      }
    );

    if (!res.ok) {
      throw new Error(`Blog API responded with status: ${res.status}`);
    }

    const data = await res.json();

    // Enhanced data transformation with error handling
    const transformedData = {
      heading: "Latest Blog Posts",
      blogs: data.data
        .map((blog: any, index: number) => {
          try {
            return {
              title: blog.attributes?.Title || `Blog Post ${index + 1}`,
              date: blog.attributes?.createdAt
                ? new Date(blog.attributes.createdAt).toLocaleDateString(
                    "en-US",
                    {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    }
                  )
                : "Recent",
              author:
                blog.attributes?.author?.data?.attributes?.name ||
                "OneAssure Team",
              description: blog.attributes?.subtitle || "",
              imageUrl: blog.attributes?.Thumbnail?.data?.attributes?.url || "",
              url: `/insurance/${
                blog.attributes?.category?.data?.attributes?.slug || "blog"
              }/${blog.attributes?.slug || ""}`,
            };
          } catch (error) {
            return null;
          }
        })
        .filter(Boolean)
        .slice(0, 6), // Limit to 6 blogs and remove null items
    };

    return transformedData;
  } catch (error) {
    // Return fallback data instead of throwing
    return {
      heading: "Latest Blog Posts",
      blogs: [],
    };
  }
}

const InsurerPage = async ({ params }: { params: { id: string } }) => {
  const { healthInsurer: rawInsurerData, siteHealthInsurerRatings } = await getInsurerData(params.id);
  const allInsurerData = await fetchAllInsurers();
  const blogData = await getBlogData();

  // Format the data to match DTO expected structure
  const formattedData: HealthInsurerApiResponse = {
    data: rawInsurerData,
    site_health_insurer_ratings: siteHealthInsurerRatings,
    success: true,
  };

  const breadcrumbs = [
    {
      name: "OneAssure",
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/`,
    },
    {
      name: "Health Insurance",
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/`,
    },
    {
      name: rawInsurerData.name,
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/${rawInsurerData.slug}-health-insurance/hi/${rawInsurerData.id}`,
    },
  ];

  const pageSchema = {
    name: formattedData.data.name,
    url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}`,
    headline: formattedData.data.health_insurer_static_content.hero_title,
    description: formattedData.data.health_insurer_static_content.verdict,
  };

  const pageNavigationSection = {
    name: "expert-review",
    itemListElement: [
      {
        position: 1,
        name: "Expert Review",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#expert-review`,
      },
      {
        position: 2,
        name: "Top Plans",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#top-plans`,
      },
      {
        position: 3,
        name: "Testimonials",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#testimonials`,
      },
      {
        position: 4,
        name: "Pros and Cons",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#pros-and-cons`,
      },
      {
        position: 5,
        name: "Documents Required",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#documents-required`,
      },
      {
        position: 6,
        name: "Claim Settlement Process",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#claim-settlement`,
      },
      {
        position: 7,
        name: "Renewal Process",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#renewal-process`,
      },
      {
        position: 8,
        name: "How to Buy",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#how-to-buy`,
      },
      {
        position: 9,
        name: "FAQs",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${formattedData.data.slug}-health-insurance/hi/${formattedData.data.id}#faqs`,
      },
    ],
  };

  const pageContent =
    "Niva Bupa Health Insurance: Experience India’s most trusted health insurance with instant cashless treatment at 10,000+ network hospitals and sum insured up to ₹3 crores. Our Review: Experts highlight wide hospital network, fast cashless claims in 30 minutes, and extensive coverage choices up to ₹3 crores. Areas for Improvement: Limited family coverage in corporate plans, longer waiting periods for pre-existing conditions. Final Verdict: Comprehensive coverage with 10,000+ hospitals, chronic and maternity cover, consumables, check-ups from Day 1, inflation-based sum increase, and discounts up to 30%. Drawbacks include no monthly premium option and higher complaint rates, but overall a solid choice. Top Plans: Niva Bupa Health Recharge (no room-rent cap, AYUSH covered), Aspire Platinum Plus (global emergency cover), ReAssure 2.0 Platinum (unlimited room rent, advanced treatments). Other Plans: ReAssure 2.0 Titanium, ReAssure 2.0 Bronze, Aspire Gold Plus. About the Company: Formerly Max Bupa, joint venture between Bupa (UK) and True North (India). Network of 10,000+ hospitals, strong claim processes, and wellness benefits. In June 2024, planned $360M IPO at $2.5B valuation. CEO Krishnan Ramachandran leads with 24+ years of experience. Company Stats: Solvency Ratio 3.9/4, ICR (own) 59.02%, ICR (market) 83.49%, Growth 3/3, Gross Premium ₹5607.57 Cr (own), ₹6623.86 Cr (market). Customer Testimonials: Stories of successful claim support from OneAssure team, including VLV Satyanarayana, Dileep Kumar Dalisi, Sahith Kurapati, Kulsum Shahiwala, Jatin Gala, Anju Renjith, and Yogesh Meena. Product Analysis: Pros – 10,000 hospitals, 30-min claims, ₹3 Cr cover. Cons – limited family cover, long waiting period. Insurance Types: Individual, Senior Citizen, NRI, Family, Maternity, Women-Centric Plans. KYC Documents: Aadhaar, Voter ID, PAN, Passport. Claim Settlement Process: Step 1 Visit hospital, Step 2 Inform OneAssure, Step 3 Carry docs, Step 4 Pre-authorization form, Step 5 Seek approval, Step 6 Claim settled directly. Reimbursement Process: Notify insurer, pay bills, collect docs, fill claim form, await review & reimbursement. Network Hospitals: 10,675+, in 114+ cities across 22 states and 4 UTs. Plan Categories: Aspire Series, Family Floater, Super Top-Up, Senior Citizen Plans. How to Buy Guide: 9 Steps covering needs assessment, research, quotes, application, documents, medical tests, approval, payment, and issuance. FAQs: How to buy from OneAssure, port policy, tax benefits (₹25,000 or ₹50,000 for seniors under 80D, plus ₹5,000 check-up deduction), cashless hospitalization availability, OneAssure services free. Other Insurers Compared: Star Health, HDFC ERGO, Care Health Insurance. Related Blogs: Latest insights on health insurance.";

  return (
    <>
      <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
      <PageSchema
        name={pageSchema.name}
        url={pageSchema.url}
        headline={pageSchema.headline}
        description={pageContent}
      />
      <FaqSchema faqs={formattedData.data.health_insurer_faqs} />
      {/* <HowToSchema data={howToSchema} />
      <RatingAndReviewSchema data={ratingAndReviewSchema} /> */}
      <TableOfContentSchema data={pageNavigationSection} />
      <Insurer
        data={formattedData}
        allInsurerData={allInsurerData}
        blogData={blogData}
      />
    </>
  );
};

export default InsurerPage;
