import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import ClaimsNew from "@/components/ClaimsNew";
import { transformData } from "@/components/ClaimsNew/dto";
import axios from "axios";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Insurance Claim - Oneassure",
  description:
    "File your insurance claims effortlessly with Oneassure. Our streamlined claims process ensures fast and fair settlements, helping you get the support you need when it matters most.",
  openGraph: {
    title: "Insurance Claim - Oneassure",
    description:
      "File your insurance claims effortlessly with Oneassure. Our streamlined claims process ensures fast and fair settlements, helping you get the support you need when it matters most.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/claims",
  },
};

const getClaims = async () => {
  const query = `
    query MyQuery {
      site_claim_page {
        title
        testimonials_title
        testimonials_subtitle
        support_title
        support_disclaimer
        subtitle
        process_title
        process_term_title
        process_health_title
        id
        claim_page_process_specifications {
          description
          icon_url
          id
          step
          title
          type
        }
        claim_page_support_items {
          icon_url
          id
          subtitle
          title
        }
        claim_page_testimonials {
          id
          name
          statement
          thumbnail_url
          background_color
        }
      }
    }

  `

  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return transformData(response.payload.data.site_claim_page[0]);
}

export default async function ClaimsPage() {
  const data = await getClaims();
  return <ClaimsNew {...data} />;
}
