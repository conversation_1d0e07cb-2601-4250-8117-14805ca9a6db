import { Metadata } from "next";
import { HealthProductRoot } from "@/components/HealthInsurance/Product";
import ProductSchema from "@/components/SchemaMarkup/ProductSchema";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";
import InsuranceSchema from "@/components/SchemaMarkup/InsuranceSchema";
import axios from "axios";
import dataTransformer from "@/components/HealthInsurance/Product/dto";

// Dynamic runtime configuration for API calls
export const dynamic = "force-dynamic";
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: { company: string; product: string };
}): Promise<Metadata> {
  // read route params

  const response = await getHealthProductData(params.product);

  return {
    title: response.data.payload.data.health_product_variants[0]
      .health_variant_seo?.meta_title
      ? response.data.payload.data.health_variant_seo?.meta_title
      : "",
    description: response.data.payload.data.health_product_variants[0]
      .health_variant_seo?.meta_description
      ? response.data.payload.data.health_product_variants[0].health_variant_seo
          ?.meta_description
      : "",
    keywords: [
      response.data.payload.data.health_product_variants[0].health_variant_seo
        ?.meta_keyword
        ? response.data.payload.data.health_product_variants[0]
            .health_variant_seo?.meta_keyword
        : "",
    ],
    openGraph: {
      title: response.data.payload.data.health_product_variants[0]
        .health_variant_seo?.meta_title
        ? response.data.payload.data.health_product_variants[0]
            .health_variant_seo?.meta_title
        : "",
      description: response.data.payload.data.health_product_variants[0]
        .health_variant_seo?.meta_description
        ? response.data.payload.data.health_product_variants[0]
            .health_variant_seo?.meta_description
        : "",
      type: "website",
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: `/health-insurance/${params.company}/${params.product}`,
    },
  };
}

const getHealthProductData = async (slug: string) => {
  const query = `query MyQuery($slug: String!) {
    health_product_variants(where: {temp_slug: {_eq: $slug}}) {
      id
      variant_slug
      variant_name
      temp_slug
      exclusions
      product {
        id
        name
        insurer {
          id
          claim_settlement_ratio
          logo_url
          name
          network_hospital_count
          network_hospital_url
          slug
          health_insurer_static_content {
            legacy
          }
        }
        product_riders {
          id
          name
          description
        }
      }
      feature_values {
        id
        value
        description
        compare_feature {
          id
          name
        }
      }
      health_variant_faqs {
        id
        question
        answer
      }
      health_variant_highlighted_features {
        id
        title
        icon_key
        description
      }
      health_variant_policy_docs {
        id
        label
        document_key
      }
      health_variant_ratings {
        id
        label
        max_score
        score
        title
      }
      health_variant_related_variants {
        id
        features
        related_variant_id
        health_variant_id
        related_variant {
          id
          variant_slug
          variant_name
          product {
            insurer {
              logo_url
              slug
              name
            }
          }
        }
      }
      health_variant_seo {
        id
        meta_description
        meta_keyword
        meta_title
        prevent_indexing
        source
      }
      health_variant_whyoneassures {
        id
        title
        description
      }
      health_variant_static_content {
        id
        product_popularity
        hero_title
        health_variant_id
        decision_guide
        comparison_enabled
        best_for
        about_the_plan
        verdict
        subtitle
        specialty
      }
    }
  }`;
  const operationName = "MyQuery";
  const variables = {
    slug: slug,
  };
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BASE_URL}/api/studio-cms`,
    {
      query,
      operationName,
      variables,
    }
  );
  return response;
};

export default async function HealthInsuranceProductPage({
  params,
}: {
  params: { company: string; product: string };
}) {
  const response = await getHealthProductData(params.product);

  const transformedData = dataTransformer(
    response.data.payload.data.health_product_variants[0]
  );

  return (
    <>
      <InsuranceSchema
        name={transformedData.variant.variant_name}
        description={transformedData.seo.meta_description || ""}
        price="0"
        sku={transformedData.variant.variant_slug || ""}
        brand={transformedData.insurer.name || ""}
        url={`${process.env.NEXT_BASE_URL}/health-insurance/${params.company}/${params.product}`}
      />

      <ProductSchema
        name={transformedData.variant.variant_name}
        description={transformedData.seo.meta_description || ""}
        image={transformedData.insurer.logo || ""}
        // price={
        //   data.data[0].attributes.variants[0].relatedVariant.data.attributes
        //     .price.data.attributes.price || 0
        // }
        // currency={
        //   data.data[0].attributes.variants[0].relatedVariant.data.attributes
        //     .currency.data.attributes.currency || "INR"
        // }
        price="0"
        currency="INR"
        sku={transformedData.variant.variant_slug || ""}
        brand={transformedData.insurer.name || ""}
        url={`${process.env.NEXT_BASE_URL}/health-insurance/${params.company}/${params.product}`}
      />
      <FaqSchema
        faqs={transformedData.faqs.map(
          (faq: { question: string; ans: string }) => ({
            question: faq.question,
            answer: faq.ans,
          })
        )}
      />
      <HealthProductRoot product={transformedData} />
    </>
  );
}
