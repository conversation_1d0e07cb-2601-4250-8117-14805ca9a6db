import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import HealthInsurance from "@/components/HealthInsurance";
import { transformData } from "@/components/HealthInsurance/dto";
import axios from "axios";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Buy Health Insurance Plan Online - OneAssure",
  description:
    "Health insurance takes care of your medical expenses. Buy best health insurance which suits your requirement. Check out guide for choosing the right health insurance plan, how it works, types of health insurance plans we offer, benefits of health insurance plans & what to look for while buying health insurance cover",
  keywords: [
    "Health Insurance",
    "Medical Health Insurance",
    "private medical insurance",
    "health cover plans",
    "medical insurance",
    "best health cover",
    "best health plan",
    "best health coverage",
    "best health coverage insurance",
  ],
  openGraph: {
    title: "Medical Health Insurance: Plans | Coverage | Policy - Oneassure",
    description:
      "Explore top Medical Health Insurance plans at Oneassure! Get comprehensive coverage and flexible policies tailored for your needs.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/health-insurance",
  },
};

async function getHealthPageData() {
  const query = `
    query MyQuery {
      site_health_lp_page {
        about_hi_description
        about_hi_thumbnail_url
        about_hi_title
        benefits_of_hi_description
        benefits_of_hi_title
        eligibility_description
        eligibility_title
        get_quote_url
        health_lp_page_benefits {
          id
          subtitle
          thumbnail_url
          title
        }
        health_lp_page_benefits_of_hi_section_datas {
          description
          icon_url
          id
          title
        }
        health_lp_page_buying_manuals {
          description
          id
          title
        }
        health_lp_page_checklists {
          description
          title
          id
        }
        health_lp_page_faqs {
          answer
          id
          question
        }
        health_lp_page_hi_checklist_section_datas {
          description
          id
          title
        }
        health_lp_page_hi_eligibilty_section_datas {
          description
          icon_url
          id
          title
        }
        health_lp_page_hi_top_plans_types {
          description
          icon_url
          id
          table_data
          title
        }
        health_lp_page_highlights {
          hightlight_title
          icon_url
          id
        }
        health_lp_page_how_to_buy_section_datas {
          id
          title
          description
        }
        health_lp_page_how_to_choose_section_datas {
          description
          icon_url
          id
          title
        }
        health_lp_page_our_experts {
          description
          id
          logo_url
          title
        }
        health_lp_page_our_partners {
          id
          logo_url
          name
          url
        }
        health_lp_page_overviews {
          description
          id
          title
        }
        health_lp_page_required_documents {
          id
          title
        }
        health_lp_page_required_documents_section_datas {
          id
          icon_url
          description
          title
        }
        health_lp_page_seo_custom_metas {
          mata_tag
          mata_value
          id
        }
        health_lp_page_testimonials {
          id
          name
          statement
          thumbnail_url
        }
        health_lp_page_why_buy_his {
          title
          id
          health_lp_page_why_buy_his_hi_importances {
            id
            health_lp_page_why_buy_his_hi_importances_reasons {
              id
              icon_url
              title
              description
            }
          }
        }
        hi_checklist_description
        hi_checklist_title
        hi_eligibility_description
        hi_eligibility_title
        hi_top_plans_description
        hi_top_plans_title
        how_to_buy_description
        how_to_buy_title
        how_to_choose_description
        how_to_choose_title
        id
        required_documents_description
        required_documents_title
        seo_meta_description
        seo_meta_keywords
        seo_meta_title
        seo_prevent_indexing
        seo_source
        subtitle
        subtitle_rich_text
        title
        top_hi_plan_description
        top_hi_plan_title
        what_is_hi_description
        what_is_hi_title
      }
    }

  `;
  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);

  return transformData(response.payload.data.site_health_lp_page[0]);
}

async function getCompanySlugsData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=health-insurance&fields[0]=name&fields[1]=slug&fields[2]=category&populate[logo][fields][0]=url&populate[health_variants][fields][0]=name&populate[health_variants][fields][1]=slug&populate[term_variants][fields][0]=name&populate[term_variants][fields][1]=slug`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }
  // console.log("Company slugs data : ", res);
  return res;
}

export default async function HealthInsurancePage() {
  const healthPageData = await getHealthPageData();
  const companySlugData = await getCompanySlugsData();

  return (
    <HealthInsurance
      data={healthPageData}
      otherCompanies={companySlugData.data}
    />
  );
}
