import { Metadata } from "next";

import CompanyRoot from "@/components/Company";
import { ratingCalculator } from "@/components/Company/utility";
import getCompanyData from "@/components/Company/actions/getCompanyData";
import { CompanyData, StatisticsProps } from "@/components/Company/types";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import { transformCompanyData, transformCompanySlugData } from "@/components/Company/dto";

export const dynamicParams = true;

export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=term-insurance&fields[0]=slug&pagination[pageSize]=100&pagination[page]=1`,
    { headers }
  ).then((res) => res.json());

  const generatedPaths = data.data.map(
    (comp: {
      attributes: {
        slug: string;
      };
    }) => ({
      company: comp.attributes.slug,
    })
  );

  //console.log("term generated comp path ", generatedPaths);
  return generatedPaths;
}

export async function generateMetadata({
  params,
}: {
  params: { company: string };
}): Promise<Metadata> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[slug][$eq]=${params.company}&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword`,
    {
      headers,
    }
  ).then((res) => res.json());

  let companyData = data.data[0];

  return {
    title: companyData.attributes.seo.metaTitle,
    description: companyData.attributes.seo.metaDescription,
    keywords: companyData.attributes.seo.keyword,
    openGraph: {
      title: companyData.attributes.seo.metaTitle,
      description: companyData.attributes.seo.metaDescription,
      type: "website",
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: `/term-insurance/${params.company}`,
    },
  };
}

async function getCompanySlugsData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=term-insurance&fields[0]=name&fields[1]=slug&fields[2]=category&[populate][logo][fields][0]=url&populate[ratings][fields][0]=solvency&populate[ratings][fields][1]=aum&populate[ratings][fields][2]=growth`,
    { headers }
  ).then((res) => res.json());
  return res;
}

async function getTermCompanySlugsData(){
  const query = `
    query MyQuery {
      term_insurers(where:{temp_slug:{_is_null: false}}){
        name
        slug
        temp_slug
        logo_url
        id
        term_insurer_ratings {
          aum
          growth
          icr
          id
          solvency
        }
      }
    }
  `;
  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return response.payload.data.term_insurers;
}

async function getTermCompanyData(slug: string){
  const query = `
    query MyQuery($slug: String!) {
      term_insurers(where:{temp_slug:{_eq:$slug}}) {
        id
        name
        claim_settlement_ratio
        logo_url
        slug
        temp_slug
        term_insurer_claim_settlements {
          id
          title
          term_insurer_claim_settlement_types {
            id
            title
            type
            term_insurer_claim_settlement_steps {
              id
              title
              description
            }
          }
        }
        temp_slug
        term_insurer_faqs {
          id
          question
          answer
        }
        term_insurer_policy_guides {
          id
          title
          term_insurer_policy_guide_points {
            id
            title
            description
          }
        }
        term_insurer_pros_cons {
          id
          title
          type
          description
        }
        term_insurer_ratings {
          id
          icr
          growth
          aum
          one_assure_rating
          solvency
        }
        term_insurer_renewal_steps {
          id
          title
          term_insurer_renewal_types {
            id
            title
            type
            term_insurer_renewal_type_steps {
              id
              title
              description
            }
          }
        }
        term_insurer_seo {
          id
          meta_description
          meta_keyword
          meta_title
          source
          prevent_indexing
        }
        term_insurer_static_content {
          id
          verdict
          legacy
          kyc_docs
          renewal_key_points
          hero_title
          customer_support_number
          customer_support_email
        }
        term_insurer_statistics {
          id
          industry
          description
          company
          type
        }
        products {
          product_variants {
            id
            variant_name
            variant_slug
            temp_slug
          }
        }
        term_insurer_network_hospital_detail {
          id
          network_hospital_count
          cities_covered
          states_and_ut
        }
      }
    }
  `;
  const variables = {
    slug: slug,
  };
  const operationName = "MyQuery";
  const response = await fetchStudioCmsData(query, operationName, variables);
  return response.payload.data.term_insurers[0];
}

export default async function TermInsuranceCompanyPage({
  params,
}: {
  params: { company: string };
}) {
  const termCompanyData = await getTermCompanyData(params.company);
  const transformedCompanyData = transformCompanyData(termCompanyData);

  const termCompanySlugData = await getTermCompanySlugsData();
  const allComp = transformCompanySlugData(termCompanySlugData);

  const data = await getCompanyData<CompanyData>(params.company);
  // const allComp = await getCompanySlugsData();

  // Solvency
  let solvency = allComp.data.map(
    (item) => item.attributes.ratings && item.attributes.ratings.solvency
  );
  solvency = [Math.min(...solvency), Math.max(...solvency)];

  // AUM
  let aum = allComp.data.map(
    (item) => item.attributes.ratings && item.attributes.ratings.aum
  );
  aum = [Math.min(...aum), Math.max(...aum)];

  // Growth
  let growth = allComp.data.map(
    (item) => item.attributes.ratings && item.attributes.ratings.growth
  );
  growth = [Math.min(...growth), Math.max(...growth)];

  const rating = [
    {
      title: "Solvency",
      rating: ratingCalculator({
        parameter: "SOLVENCY",
        minRating: 2,
        maxRating: 4,
        paramCurrentValue: allComp.data[0].attributes.ratings.solvency!,
        paramMaxValue: solvency[1],
        paramMinValue: solvency[0],
      }),
      outOf: 4,
    },
    {
      title: "AUM",
      rating: ratingCalculator({
        parameter: "AUM",
        minRating: 1.5,
        maxRating: 3,
        paramCurrentValue: allComp.data[0].attributes.ratings.aum!,
        paramMaxValue: aum[1],
        paramMinValue: aum[0],
      }),
      outOf: 3,
    },
    {
      title: "Growth",
      rating: ratingCalculator({
        parameter: "GROWTH",
        minRating: 1.5,
        maxRating: 3,
        paramCurrentValue: allComp.data[0].attributes.ratings.growth!,
        paramMaxValue: growth[1],
        paramMinValue: growth[0],
      }),
      outOf: 3,
    },
  ];
  const statisticsData: StatisticsProps = {
    companyName: transformedCompanyData.attributes.name,
    graphs: [
      {
        title: "Premium Underwritten",
        industry:
          transformedCompanyData.attributes.statistics.premiumUnderwritten!.industry,
        company:
          transformedCompanyData.attributes.statistics.premiumUnderwritten!.company,
        suffix: "",
        description:
          transformedCompanyData.attributes.statistics.premiumUnderwritten?.description,
      },
      {
        title: "Solvency Ratio",
        industry: transformedCompanyData.attributes.statistics.solvencyRatio!.industry,
        company: transformedCompanyData.attributes.statistics.solvencyRatio!.company,
        suffix: "",
        description:
          transformedCompanyData.attributes.statistics.solvencyRatio?.description,
      },
    ],
    rating: rating,
  };
  return (
    <CompanyRoot
      company={transformedCompanyData}
      category={"term"}
      allComp={allComp.data}
      statistics={statisticsData}
    />
  );
}
