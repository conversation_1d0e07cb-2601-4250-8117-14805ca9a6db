import { Metadata } from "next";

import getTermVariantData from "@/components/LifeInsurance/actions/getVariantData";
import { TermVariantResponse } from "@/components/LifeInsurance/types";
import TermProductRoot from "@/components/LifeInsurance/Product";
import ProductSchema from "@/components/SchemaMarkup/ProductSchema";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";
import InsuranceSchema from "@/components/SchemaMarkup/InsuranceSchema";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import { transformTermProductData } from "@/components/LifeInsurance/Product/dto";

export const dynamicParams = true;

/** The top-down approach does not work. See https://github.com/vercel/next.js/issues/53717 */
export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=term-insurance&fields[0]=slug&populate[term_variants][fields][0]=slug&pagination[pageSize]=100`,
    { headers }
  ).then((res) => res.json());

  const generatedPaths = data.data.flatMap(
    (company: {
      attributes: {
        slug: string;
        term_variants: {
          data: { attributes: { slug: string } }[];
        };
      };
    }) => {
      return company.attributes.term_variants.data.map((variant) => ({
        product: variant.attributes.slug,
        company: company.attributes.slug,
      }));
    }
  );
  return generatedPaths;
}

export async function generateMetadata({
  params,
}: {
  params: { company: string; product: string };
}): Promise<Metadata> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/term-variants?filters[slug][$eq]=${params.product}&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword`,
    { headers }
  ).then((res) => res.json());

  const product = data.data[0];

  return {
    title: product.attributes.seo?.metaTitle
      ? product.attributes.seo?.metaTitle
      : "",
    description: product.attributes.seo?.metaDescription
      ? product.attributes.seo?.metaDescription
      : "",
    keywords: [product.attributes.seo ? product.attributes.seo?.keyword : ""],
    openGraph: {
      title: product.attributes.seo?.metaTitle
        ? product.attributes.seo?.metaTitle
        : "",
      description: product.attributes.seo?.metaDescription
        ? product.attributes.seo?.metaDescription
        : "",
      type: "website",
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: `/term-insurance/${params.company}/${params.product}`,
    },
  };
}

async function getProductData(slug: string) {
  const query = `
    query MyQuery($temp_slug: String!) {
      term_product_variants(where:{temp_slug: {_eq: $temp_slug}}) {
        id
        variant_name
        variant_slug
        temp_slug
        product {
          insurer {
            name
            logo_url
            id
            temp_slug
            term_insurer_ratings {
              id
              solvency
              icr
              growth
              aum
              one_assure_rating
            }
            term_insurer_static_content {
              legacy
            }
          }
        }
        term_variant_addons {
          id
          title
          description
        }
        term_variant_eligibility {
          id
          title
          description
        }
        term_variant_faqs {
          id
          question
          answer
        }
        term_variant_features {
          id
          title
          listed_features
          description
        }
        term_variant_notes {
          id
          title
          description
        }
        term_variant_policy_docs {
          id
          label
          document_key
        }
        term_variant_related_variants {
          id
          term_variant_id
          related_variant_id
          features
          related_variant {
            id
            variant_name
            variant_slug
            product {
              insurer {
                name
                slug
                logo_url
              }
            }
          }
        }
        term_variant_seo {
          id
          meta_title
          meta_description
          keywords
          prevent_indexing
          source
        }
        term_variant_static_content {
          id
          hero_title
          exclusions
          claim_settlement_ratio
          about_the_plan
          smoking_downsides
          verdict
          women_benefits
        }
        term_variant_why_one_assures {
          id
          title
          description
        }
      }
    }
  `
  const operationName = "MyQuery";
  const variables = {
    temp_slug: slug,
  };
  const response = await fetchStudioCmsData(query, operationName, variables);
  return response.payload.data.term_product_variants[0];
}

export default async function TermInsuranceProductPage({
  params,
}: {
  params: { company: string; product: string };
}) {
  const data = await getTermVariantData<TermVariantResponse>(params.product);
  const productData = await getProductData(params.product);
  // console.log("term variant data", data.data[0].attributes.variants);

  const transformedData = transformTermProductData(productData);

  return (
    <>
      <InsuranceSchema
        name={data.data[0].attributes.name}
        description={data.data[0].attributes.seo?.metaDescription || ""}
        price="0"
        sku={data.data[0].attributes.slug || ""}
        brand={data.data[0].attributes.company.data.attributes.name || ""}
        url={`${process.env.NEXT_BASE_URL}/term-insurance/${params.company}/${params.product}`}
      />

      <ProductSchema
        name={data.data[0].attributes.name}
        description={data.data[0].attributes.seo?.metaDescription || ""}
        image={
          data.data[0].attributes.company.data.attributes.logo.data.attributes
            .url || ""
        }
        price="0"
        currency="INR"
        sku={data.data[0].attributes.slug || ""}
        brand={data.data[0].attributes.company.data.attributes.name || ""}
        url={`${process.env.NEXT_BASE_URL}/term-insurance/${params.company}/${params.product}`}
      />
      <FaqSchema
        faqs={data.data[0].attributes.faqs.map((faq) => ({
          question: faq.question,
          answer: faq.ans,
        }))}
      />
      <TermProductRoot product={transformedData} />
    </>
  );
}
