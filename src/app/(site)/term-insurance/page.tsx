import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import LifeInsurance from "@/components/LifeInsurance";
import { transformData } from "@/components/LifeInsurance/dto";
import axios from "axios";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Best term life Insurance Plan in India | OneAssure",
  description:
    "With the Best Term Life Insurance Plan in India protect your loved ones from financial loss in the event of death. Buy term insurance with oneassure now!",
  keywords: [
    "term insurance",
    "life insurance",
    "term insurance plan",
    "term life policy",
    "term coverage life insurance",
  ],
  openGraph: {
    title: "Best term life Insurance Plan in India | OneAssure",
    description:
      "With the Best Term Life Insurance Plan in India protect your loved ones from financial loss in the event of death. Buy term insurance with oneassure now!",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: `/term-insurance`,
  },
};


async function getCompanySlugsData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=term-insurance&pagination[pageSize]=100&fields[0]=name&fields[1]=slug&fields[2]=category&populate[logo][fields][0]=url&populate[health_variants][fields][0]=name&populate[health_variants][fields][1]=slug&populate[term_variants][fields][0]=name&populate[term_variants][fields][1]=slug`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }
  // console.log("Company slugs data : ", res);
  return res;
}


async function getTermPageData() {
  const query = `
    query MyQuery {
      site_term_lp_page {
        about_ti_description
        about_ti_thumbnail_url
        about_ti_title
        benefits_of_ti_description
        benefits_of_ti_title
        eligibility_description
        get_quote_url
        how_does_ti_works_description
        how_does_ti_works_title
        how_ti_works_description
        how_to_buy_ti_description
        how_to_buy_ti_oneassure
        how_to_buy_ti_oneassure_description
        how_to_buy_ti_title
        required_documents_description
        id
        required_documents_title
        ride_for_ti_description
        ride_for_ti_title
        seo_meta_description
        seo_meta_keywords
        seo_meta_title
        seo_prevent_indexing
        seo_source
        subtitle
        subtitle_rich_text
        ti_eligibility_description
        ti_eligibility_title
        title
        what_is_ti_description
        what_is_ti_title
        who_buys_ti_description
        who_should_buy_ti_description
        who_should_buy_ti_title
        who_to_buy_ti_description
        who_to_buy_ti_title
        term_lp_page_benefits {
          icon_url
          id
          subtitle
          title
        }
        term_lp_page_benefits_of_tis {
          description
          icon_url
          id
          title
        }
        term_lp_page_buy_from_oas {
          description
          id
          title
        }
        term_lp_page_documents {
          id
          title
        }
        term_lp_page_faqs {
          answer
          id
          question
        }
        term_lp_page_highlights {
          hightlight_title
          icon_url
          id
        }
        term_lp_page_how_to_buy_tis {
          description
          id
          title
        }
        term_lp_page_how_to_buys {
          description
          id
          title
        }
        term_lp_page_how_to_ti_onesassures {
          description
          id
          title
        }
        term_lp_page_importances {
          id
          subtitle
          thumbnail_url
          title
        }
        term_lp_page_our_partners {
          id
          logo_url
          name
          url
        }
        term_lp_page_required_docs_tis {
          description
          icon_url
          id
          title
        }
        term_lp_page_riders {
          description
          id
          title
        }
        term_lp_page_rides_for_tis {
          description
          icon_url
          id
          title
        }
        term_lp_page_seo_custom_metas {
          id
          meta_tag
          meta_value
        }
        term_lp_page_testimonials {
          background_color
          id
          name
          statement
          thumbnail_url
        }
        term_lp_page_ti_eligibilitys {
          icon_url
          description
          id
          title
        }
        term_lp_page_who_should_buy_tis {
          description
          icon_url
          id
          title
        }
        term_lp_page_who_to_buy_tis {
          description
          icon_url
          id
          title
        }
        term_lp_page_why_buy_tis {
          background_color
          description
          id
          thumbnail_url
          title
        }
        term_lp_what_we_offers {
          term_lp_what_we_offer_icons {
            id
          }
        }
      }
      site_term_lp_page_benefits_of_ti {
        description
        icon_url
        id
        title
      }
      site_term_lp_page_buy_from_oa {
        description
        id
        title
      }
      site_term_lp_page_documents {
        id
        title
      }
      site_term_lp_page_faqs {
        question
        answer
        id
      }
      site_term_lp_page_highlights {
        hightlight_title
        icon_url
        id
      }
      site_term_lp_page_how_to_buy_ti {
        description
        id
        title
      }
      site_term_lp_page_how_to_buys {
        description
        id
        title
      }
      site_term_lp_page_how_to_ti_onesassure {
        id
        description
        title
      }
      site_term_lp_page_importances {
        id
        subtitle
        thumbnail_url
        title
      }
      site_term_lp_page_our_partners {
        id
        logo_url
        name
        url
      }
      site_term_lp_page_required_docs_ti {
        description
        icon_url
        id
        title
      }
      site_term_lp_page_riders {
        description
        id
        title
      }
      site_term_lp_page_seo_custom_metas {
        id
        meta_tag
        meta_value
      }
      site_term_lp_page_testimonials {
        background_color
        id
        name
        statement
        thumbnail_url
      }
      site_term_lp_page_ti_eligibilitys {
        description
        icon_url
        id
        title
      }
      site_term_lp_page_who_should_buy_ti {
        description
        icon_url
        id
        title
      }
      site_term_lp_page_who_to_buy_ti {
        description
        icon_url
        id
        title
      }
      site_term_lp_page_rides_for_ti {
        description
        icon_url
        id
        title
      }
      site_term_lp_page_why_buy_tis {
        background_color
        description
        id
        thumbnail_url
        title
      }
      site_term_lp_what_we_offer {
        id
        subtitle
        title
        term_lp_what_we_offer_icons {
          icon_url
          id
        }
      }
    }

  `

  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return transformData(response.payload.data.site_term_lp_page[0]);
}

export default async function LifeInsurancePage() {
  const termPageData = await getTermPageData();
  const companySlugData = await getCompanySlugsData();
  return (
    <LifeInsurance
      data={termPageData}
      otherCompanies={companySlugData.data}
    />
  );
}
