import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import Document from "@/components/Document";
import { transformData } from "@/components/Document/dto";

export const dynamicParams = false;

export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/oa-docs?fields[0]=slug&pagination[pageSize]=100&pagination[page]=1`,
    { headers }
  ).then((res) => res.json());

  const generatedPaths = data.data.map(
    (doc: {
      attributes: {
        slug: string;
      };
    }) => ({
      doc: doc.attributes.slug,
    })
  );

  return generatedPaths;
}
const getDocData = async (slug: string) => {
  const query = `
    query MyQuery($slug: String!) {
      site_oa_doc(where:{slug:{_eq:$slug}}) {
        content
        id
        show_doc
        slug
        title
        oa_doc_files {
          file_key
          id
          label
        }
      }
    }

  `

  const operationName = "MyQuery";
  const variables = {slug: slug};
  const response = await fetchStudioCmsData(query, operationName, variables);

  return transformData(response.payload.data.site_oa_doc[0]);
}

interface DocsPageProps {
  params: { doc: string };
}

export default async function Docs({ params }: DocsPageProps) {
  const data = await getDocData(params.doc);
  ;
  
  
  return <Document desc={data} path={["home", `documents/${params.doc}`]} />;
}
