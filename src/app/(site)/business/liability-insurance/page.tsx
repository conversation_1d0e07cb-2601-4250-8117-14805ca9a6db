import { Metadata } from "next";
import Liability from "@/components/Business/Liability";
import { transformData } from "@/components/Business/Liability/dto";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";

export const metadata: Metadata = {
  title:
    "Book an appointment with OneAssure | Best insurance solution for businesses in India",
  description:
    "If you have any queries about how we can solve your organization's insurance needs, then please book an appointment with us. We promise to answer your questions to your satisfaction.",
};

async function getLiabilityPageData() {
  const query = `
    query MyQuery {
      site_liability_lp_page {
        id
        title
        thumbnail_url
        liability_lp_page_whyus_reasons {
          background_color
          id
          description
          thumbnail_url
          title
        }
        liability_lp_page_highlights {
          hightlight_title
          icon_url
          id
        }
        liability_lp_page_cover_items {
          description
          icon_url
          id
          title
        }
      }
    }
  `;

  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);

  return transformData(response.payload.data.site_liability_lp_page[0]);
}

export default async function LiabilityInsurancePage() {
  const data = await getLiabilityPageData();
  return <Liability data={data} />;
}
