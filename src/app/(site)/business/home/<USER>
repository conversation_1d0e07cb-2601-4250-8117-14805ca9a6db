import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import BusinessHome from "@/components/Business/Home";
import { transformData } from "@/components/Business/Home/dto";
import { Metadata } from "next";

export const metadata: Metadata = {
  title:
    "Book an appointment with OneAssure | Best insurance solution for businesses in India",
  description:
    "If you have any queries about how we can solve your organization's insurance needs, then please book an appointment with us. We promise to answer your questions to your satisfaction.",
  openGraph: {
    title:
      "Book an appointment with OneAssure | Best insurance solution for businesses in India",
    description:
      "If you have any queries about how we can solve your organization's insurance needs, then please book an appointment with us. We promise to answer your questions to your satisfaction.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: `/business/home`,
  },
};

async function getBusinessLPData() {
  const query = `
    query MyQuery {
      site_business_lp {
        title
        thumbnail_url
        subtitle
        id
        business_lp_benefits {
          description
          icon_url
          id
          tag
          title
        }
        business_lp_clients {
          id
          logo_url
          name
        }
        business_lp_reasons {
          description
          id
          thumbnail_url
          title
          background_color
        }
        business_lp_testimonials {
          id
          name
          statement
          thumbnail_url
          background_color
        }
      }
    }
  `

  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return transformData(response.payload.data.site_business_lp[0]);
}

export default async function BusinessHomePage() {
  const data = await getBusinessLPData();
  return <BusinessHome data={data} />;
}
