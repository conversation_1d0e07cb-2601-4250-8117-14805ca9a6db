import { Metada<PERSON> } from "next";
import GroupHealthLife from "@/components/Business/GroupHL";
import transformData from "@/components/Business/GroupHL/dto";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";

export const metadata: Metadata = {
  title:
    "Book an appointment with OneAssure | Best insurance solution for businesses in India",
  description:
    "If you have any queries about how we can solve your organization's insurance needs, then please book an appointment with us. We promise to answer your questions to your satisfaction.",
};

async function getGroupLPData(){
  const query = `
    query MyQuery {
      site_group_lp_page {
        id
        title
        group_lp_page_benefit_items {
          id
          tag
          title
          icon_url
          description
        }
        group_lp_page_faqs {
          id
          question
          answer
        }
        group_lp_page_features {
          id
          tag
          title
          icon_url
          description
        }
        group_lp_page_highlights {
          id
          icon_url
          hightlight_title
        }
        group_lp_page_our_partners {
          id
          name
          logo_url
          url
        }
        group_lp_page_reasons {
          id
          thumbnail_url
          title
          description
          background_color
        }
        group_lp_page_testimonials {
          id
          name
          statement
          thumbnail_url
          background_color
        }
      }
    }
  `;

  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return transformData(response.payload.data.site_group_lp_page[0]);
}

export default async function GroupHealthLifePage() {
  const data = await getGroupLPData();

  return <GroupHealthLife data={data} />;
}
