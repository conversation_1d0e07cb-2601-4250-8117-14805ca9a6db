import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import ContactUsNew from "@/components/ContactUsNew";
import { transformData } from "@/components/ContactUsNew/dto";
import axios from "axios";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Contact us - Oneassure",
  description:
    "Reach out to Oneassure for all your insurance inquiries. Our expert team is ready to assist you with solutions and support, ensuring your insurance experience is smooth and hassle-free.",
  openGraph: {
    title: "Contact us - Oneassure",
    description:
      "Reach out to Oneassure for all your insurance inquiries. Our expert team is ready to assist you with solutions and support, ensuring your insurance experience is smooth and hassle-free.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/contact-us",
  },
};

const getContactUs = async () => {
  const query = `
    query MyQuery {
      site_contact_us_page {
        id
        subtitle
        title
        contact_us_page_cards {
          icon_url
          id
          title
          value
        }
      }
    }
  `
  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return transformData(response.payload.data.site_contact_us_page[0]);
}


export default async function ContactUsPage() {
  const data = await getContactUs();
  return <ContactUsNew data={data} />;
}
