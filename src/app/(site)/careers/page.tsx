import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import CareersNew from "@/components/CareersNew";
import { transformData } from "@/components/CareersNew/dto";
import axios from "axios";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Careers | Oneassure",
  openGraph: {
    title: "Careers | Oneassure",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/careers",
  },
};

const getCareers = async () => {
  const query = `
    query MyQuery {
      site_career_page {
        benefits_title
        title
        testimonials_title
        team_title
        team_subtitle
        subtitle
        id
        job_title
        career_page_benefits {
          icon_url
          id
          subtitle
          title
        }
        career_page_jobs {
          department
          id
          job_title
          link
          location
          type
          posted_date
        }
        career_page_team_images {
          id
          image_url
        }
        career_page_testimonials {
          background_color
          id
          name
          statement
          thumbnail_url
        }
      }
    }
  `

  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);

  return transformData(response.payload.data.site_career_page[0]);
}

export default async function CareersPage() {
  const data = await getCareers();
  return <CareersNew {...data} />;
}
