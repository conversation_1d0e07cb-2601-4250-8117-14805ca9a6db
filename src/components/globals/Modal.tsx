"use client";
import { Dialog } from "@headlessui/react";
import { CalendarDaysIcon } from "@heroicons/react/24/outline";
import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";

const Modal = ({
  open,
  handleModal,
  msg,
  utmSrc,
}: {
  open: boolean;
  handleModal: () => void;
  msg?: string;
  utmSrc?: string;
}) => {
  const search = useSearchParams();
  const utm = search.get("utm_source");
  return (
    <Dialog
      open={open}
      onClose={handleModal}
      className="fixed inset-0 z-[100] overflow-y-auto"
    >
      <div className="flex items-end justify-center min-h-screen pt-4 md:px-4 pb-20 text-center sm:block sm:p-0">
        <Dialog.Overlay className="fixed inset-0 transition-opacity bg-gray-500 opacity-75" />

        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        <Dialog.Panel
          className={`inline-block align-bottom bg-white rounded-3xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full  md:max-w-7xl relative`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
        >
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -z-40">
            <CalendarDaysIcon className="w-40 h-40 text-gray900 opacity-50 " />
            <h2 className="text-gray900 text-xl text-center opacity-50">
              LOADING...
            </h2>
          </div>

          <iframe
            src={`https://calendly.com/oneassure/30min?year=2024&a1=${msg}&utm_source=${utmSrc}${
              utm ? `+${utm}` : ""
            }`}
            title="Calendly"
            className="h-[85vh] w-full z-50"
          ></iframe>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default Modal;
