"use client";
import SectionHeader from "@/components/globals/SectionHeader";
import Image from "next/image";
import React from "react";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import { IoTrendingUpSharp } from "react-icons/io5";
import { Button } from "@/components/UI/Button";
import {
  BodyLarge,
  BodySmall,
  HeadingMedium,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";

export type Category = {
  icon: string;
  title: string;
  button: string;
  features: string[];
  buttonColor: string;
  cardBg: string;
  cardBorder: string;
  mostPopular?: boolean;
  redirectUrl?: string;
};

type CategoryCardsProps = {
  pill: string;
  heading: string;
  subHeading?: string;
  categories: Category[];
  id?: string;
};

// Desktop Component - Most popular card scaled, others equal height
const DesktopCards: React.FC<{ categories: Category[] }> = ({ categories }) => {
  function handleClick() {
    const pathname = window.location.pathname;
    const utm_medium = sessionStorage.getItem("utm_medium");
    const utm_source = sessionStorage.getItem("utm_source");
    const utm_campaign = sessionStorage.getItem("utm_campaign");
    const utm_content = sessionStorage.getItem("utm_content");
    const utm_term = sessionStorage.getItem("utm_term");

    const script = document.createElement("script");
    script.src = "https://assets.calendly.com/assets/external/widget.js";
    script.async = true;
    document.head.appendChild(script);

    const link = document.createElement("link");
    link.href = "https://assets.calendly.com/assets/external/widget.css";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    script.onload = function () {
      if (window.Calendly) {
        const currentPath = pathname;
        const mediumParam = utm_medium
          ? `${currentPath}_${utm_medium}`
          : currentPath;

        window.Calendly.initPopupWidget({
          url: `https://calendly.com/oneassure/30min?year=2024&&utm_source=${utm_source}&utm_medium=${mediumParam}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`,
        });
      }
    };
    return false;
  }

  return (
    <div className="w-full grid grid-cols-3 gap-4 items-stretch">
      {categories.map((plan, idx) => (
        <div
          key={idx}
          className={`relative flex flex-col justify-center items-center rounded-2xl w-full p-4 md:px-5 transition-all duration-300 text-center ${
            plan.mostPopular
              ? "scale-y-105 border-2 border-primary-300 shadow-lg md:pt-8 md:pb-4 order-first md:order-none"
              : "md:py-4 shadow-sm border border-primary-200 h-full"
          }`}
        >
          {plan.mostPopular && (
            <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-secondary-100 px-4 rounded-full font-medium z-10 border-[1px] border-secondary-400">
              <BodySmall className="text-secondary-400 flex items-center gap-2">
                <IoTrendingUpSharp className="text-secondary-400" /> Most
                Popular
              </BodySmall>
            </div>
          )}
          <div
            className={`flex flex-col items-center ${
              plan.mostPopular ? "gap-3 md:gap-4" : "gap-3"
            } w-full h-full justify-between`}
          >
            <div className="flex flex-col items-center gap-3">
              <div className="flex justify-center items-center">
                <Image
                  src={plan.icon}
                  alt={plan.title}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full"
                />
              </div>

              <HeadingMedium
                weight="semibold"
                className="text-neutral-1100 text-center"
              >
                {plan.title}
              </HeadingMedium>

              <ul className="space-y-2 w-full">
                {plan.features.map((feature, i) => (
                  <li key={i} className="flex text-left">
                    <IoMdCheckmarkCircleOutline className="text-green-main-200 mr-2 flex-shrink-0 mt-1" />
                    <BodyLarge className="text-neutral-800">
                      {feature}
                    </BodyLarge>
                  </li>
                ))}
              </ul>
            </div>
            <Button
              variant="primary"
              className="mt-4 md:mt-6"
              onClick={() => {
                plan.redirectUrl? window.location.href = plan.redirectUrl : handleClick();
              }}
            >
              <span className="text-white text-sm">{plan.button}</span>
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};

// Mobile Component - Carousel with pagination dots
const MobileCards: React.FC<{ categories: Category[] }> = ({ categories }) => {
  function handleClick() {
    const pathname = window.location.pathname;
    const utm_medium = sessionStorage.getItem("utm_medium");
    const utm_source = sessionStorage.getItem("utm_source");
    const utm_campaign = sessionStorage.getItem("utm_campaign");
    const utm_content = sessionStorage.getItem("utm_content");
    const utm_term = sessionStorage.getItem("utm_term");

    const script = document.createElement("script");
    script.src = "https://assets.calendly.com/assets/external/widget.js";
    script.async = true;
    document.head.appendChild(script);

    const link = document.createElement("link");
    link.href = "https://assets.calendly.com/assets/external/widget.css";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    script.onload = function () {
      if (window.Calendly) {
        const currentPath = pathname;
        const mediumParam = utm_medium
          ? `${currentPath}_${utm_medium}`
          : currentPath;

        window.Calendly.initPopupWidget({
          url: `https://calendly.com/oneassure/30min?year=2024&&utm_source=${utm_source}&utm_medium=${mediumParam}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`,
        });
      }
    };
    return false;
  }

  return (
    <div className="w-full">
      <MobileCarousel totalSlides={categories.length}>
        {categories.map((plan, index) => (
          <MobileCarouselItem key={index} className="pt-2 md:pt-0">
            <div className="relative flex flex-col justify-center items-center rounded-xl w-full p-4 transition-all duration-300 text-center shadow-md border border-primary-300 h-full">
              {plan.mostPopular && (
                <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-secondary-100 px-4 rounded-full font-medium z-10 border-[1px] border-secondary-400">
                  <BodySmall className="text-secondary-400 flex items-center gap-2">
                    <IoTrendingUpSharp className="text-secondary-400" /> Most
                    Popular
                  </BodySmall>
                </div>
              )}
              <div className="flex flex-col items-center gap-3 w-full h-full justify-between">
                <div className="flex flex-col items-center gap-3 h-full">
                  <div className="flex justify-center items-center">
                    <Image
                      src={plan.icon}
                      alt={plan.title}
                      width={48}
                      height={48}
                      className="w-12 h-12 rounded-full"
                    />
                  </div>

                  <HeadingMedium
                    weight="semibold"
                    className="text-neutral-1100 text-center"
                  >
                    {plan.title}
                  </HeadingMedium>

                  <ul className="space-y-2 w-full">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex text-left">
                        <IoMdCheckmarkCircleOutline className="text-green-main-200 mr-2 flex-shrink-0 mt-1" />
                        <BodyLarge className="text-neutral-800">
                          {feature}
                        </BodyLarge>
                      </li>
                    ))}
                  </ul>
                </div>
                <Button
                  variant="primary"
                  className="mt-6"
                  onClick={() => {
                    plan.redirectUrl? window.location.href = plan.redirectUrl : handleClick();
                  }}
                >
                  <span className="text-white text-sm">{plan.button}</span>
                </Button>
              </div>
            </div>
          </MobileCarouselItem>
        ))}
      </MobileCarousel>
    </div>
  );
};

const CategoryCards: React.FC<CategoryCardsProps> = ({
  pill,
  heading,
  subHeading,
  categories,
  id,
}) => {
  return (
    <SectionContainerLarge
      className="w-full flex flex-col items-center !px-0"
      id={id}
    >
      <SectionHeader
        pill={pill}
        heading={heading}
        subheading={subHeading}
        component="h2"
        className="px-6 md:px-0"
      />

      {/* Desktop Layout */}
      <div className="hidden md:block w-full mt-3 md:mt-2">
        <DesktopCards categories={categories}/>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden w-full">
        <MobileCards categories={categories}/>
      </div>
    </SectionContainerLarge>
  );
};

export default CategoryCards;
