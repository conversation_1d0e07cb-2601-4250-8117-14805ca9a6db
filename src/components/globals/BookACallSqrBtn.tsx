"use client";

import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { forwardRef, useEffect, useRef } from "react";
import { IoCallOutline } from "react-icons/io5";
import { useSessionStorage } from "usehooks-ts";
import { usePathname } from "next/navigation";
import { Button } from "@/components/UI/Button";

const BookACallSqrBtn = forwardRef(function BookACallSqrBtn(
  {
    onClick,
    className,
    label = "Book a Call",
    utm,
    ...props
  }: {
    onClick?: () => void;
    className?: string;
    label?: string;
    utm?: string;
    [key: string]: any;
  },
  ref
) {
  const pathname = usePathname();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", "");
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", "");
  const [utm_campaign, setUtmCampaign] = useSessionStorage("utm_campaign", "");
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", "");
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", "");

  useEffect(() => {
    function isCalendlyEvent(e: MessageEvent) {
      return (
        e.origin === "https://calendly.com" &&
        e.data.event &&
        e.data.event.indexOf("calendly.") === 0
      );
    }

    function handleCalendlyMessage(e: MessageEvent) {
      if (isCalendlyEvent(e)) {
        // console.log("Event name:", e.data.event);
        // console.log("Event details:", e.data.payload);
      }
    }

    if (typeof window !== "undefined") {
      window.addEventListener("message", handleCalendlyMessage);
    }

    return () => {
      window.removeEventListener("message", handleCalendlyMessage);
    };
  }, []);

  function handleClick() {
    const script = document.createElement("script");
    script.src = "https://assets.calendly.com/assets/external/widget.js";
    script.async = true;
    document.head.appendChild(script);

    const link = document.createElement("link");
    link.href = "https://assets.calendly.com/assets/external/widget.css";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    // if (window.Calendly) {
    //   window.Calendly.initPopupWidget({
    //     url: "https://calendly.com/oneassure/30min?year=2024",
    //   });
    // }

    script.onload = function () {
      if (window.Calendly) {
        // Get the pathname without domain
        const currentPath = pathname;
        // If utm_medium exists in session, append it to pathname
        const mediumParam = utm_medium
          ? `${currentPath}_${utm_medium}`
          : currentPath;

        window.Calendly.initPopupWidget({
          url: `https://calendly.com/oneassure/30min?year=2024&&utm_source=${utm_source}&utm_medium=${mediumParam}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`,
        });
      }
    };
    return false;
  }

  return (
    <Button
      onClick={handleClick}
      id="calendly-inline-widget"
      className={className}
      {...props}
      // @ts-ignore
      ref={ref}
    >
      <IoCallOutline className="text-xl text-white mr-1" />
      {label}
    </Button>
  );
});

export default BookACallSqrBtn;
