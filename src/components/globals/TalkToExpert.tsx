"use client";

const TalkToExpertBtn = ({ onClick }: { onClick?: () => void }) => {
  // function handleClick() {
  //   // @ts-ignore
  //   window.Calendly.initPopupWidget({
  //     url: "https://calendly.com/oneassure/30min?year=2024",
  //   });
  //   return false;
  // }
  return (
    <>
      <button
        id="calendly-inline-widget"
        className=" border-2 border-blue600 text-blue600 font-semibold py-3 px-6 md:px-8 rounded-lg cursor-pointer"
        onClick={onClick}
      >
        Talk to Expert
      </button>
    </>
  );
};

export default TalkToExpertBtn;
