export interface Hero {
  title: string;
  rating: number;
}

export interface Logo {
  data: {
    attributes: {
      url: string;
    };
  };
}
export interface AccordianType {
  title: string;
  description: string;
}

export type SlugsAttribute = {
  name: string;
  slug: string;
  category: string | null;
  hero: Hero;
  logo: Logo;
};

export type AllCompSlugs = {
  id: number | string;
  attributes: SlugsAttribute;
};

export type Blog = {
  Title: string;
  slug: string;
  subtitle: string;
  category: {
    data: {
      attributes: {
        slug: string;
      };
    };
  };
  Thumbnail: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
};

export type VariantFeatures = {
  title: string;
  description: string;
  listedFeatures: {
    feature: string;
  }[];
}[];

export type PolicyDocs = {
  label: string;
  document: {
    data: [
      {
        attributes: {
          url: string;
        };
      }
    ];
  };
}[];

export type HighlightedFeatures = {
  title: string;
  description: string;
  icon: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
}[];

export type Variant = {
  id: number;
  attributes: {
    name: string;
    slug: string;
    logo: string;
    companyName: string;
    companySlug: string;
  };
};
export type RelatedVariants = {
  id: number | string;
  relatedVariant: {
    data: {
      id: number | string;
      attributes: {
        name: string;
        slug: string;
        company: {
          data: {
            attributes: {
              name: string;
              slug: string;
              logo: {
                data: {
                  attributes: {
                    url: string;
                  };
                };
              };
            };
          };
        };
      };
    };
  };
  features: any[];
}[];

export type VariantCompany = {
  name: string;
  slug: string;
  logo: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
  legecy?: string;
 
};
