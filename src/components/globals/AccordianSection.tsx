import React, { useState, useEffect } from "react";
import { IoChevronDownOutline, IoChevronUp } from "react-icons/io5";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import PillBadge from "@/components/globals/PillBadge";
import { BodyLarge, HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { htmlParser } from "@/utils/htmlParser";

type FAQ = {
  question: string;
  answer: string;
};

type FAQsProps = {
  pill: string;
  heading: string;
  subheading: string;
  faqs: FAQ[];
  id: string;
  toggleComponent?: React.ReactNode;
  sectionTitle?: string;
};

const FAQs: React.FC<FAQsProps> = ({
  pill,
  heading,
  subheading,
  faqs,
  id,
  toggleComponent,
  sectionTitle,
}) => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const faqsPerPage = 5;

  useEffect(() => {
    setCurrentPage(1);
    setOpenIndex(null);
  }, [faqs]);

  // Calculate pagination
  const totalPages = Math.ceil(faqs.length / faqsPerPage);
  const startIndex = (currentPage - 1) * faqsPerPage;
  const endIndex = startIndex + faqsPerPage;
  const currentFaqs = faqs.slice(startIndex, endIndex);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
    setOpenIndex(null);
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
    setOpenIndex(null);
  };

  return (
    <SectionContainerLarge
      className="w-full flex flex-col md:flex-row items-center md:justify-between gap-6 md:gap-0"
      id={id}
    >
      {/* Left: Heading and subheading */}
      <div className="w-full md:w-[26%] flex flex-col items-center justify-center gap-2 md:gap-6 ">
        <PillBadge pill={pill} />
        <div>
          <HeadingXLarge
            as="h2"
            className=" text-neutral-1100 text-center mb-2 md:mb-3"
          >
            {heading}
          </HeadingXLarge>

          {htmlParser(subheading, {
            components: { p: BodyLarge },
            classNames: { p: "text-neutral-1100 text-center" },
          })}
        </div>
      </div>
      {/* Right: Toggle and FAQ Accordions */}
      <div className="w-full md:w-[60%] flex flex-col gap-4 md:gap-6  md:px-0">
        {/* Toggle Component - Render above accordions when provided */}
        {toggleComponent && (
          <div className="flex justify-center">{toggleComponent}</div>
        )}

        {currentFaqs.map((faq, idx) => (
          <div
            key={idx}
            className="bg-white border border-blue-200 rounded-xl p-4 md:px-5 shadow"
          >
            <button
              className="w-full flex justify-between items-center text-left focus:outline-none"
              onClick={() =>
                setOpenIndex(
                  openIndex === startIndex + idx ? null : startIndex + idx
                )
              }
            >
              <BodyLarge weight="medium" className="text-neutral-1100">
                {faq.question}
              </BodyLarge>
              {openIndex === startIndex + idx ? (
                <IoChevronUp size={20} className="flex-shrink-0" />
              ) : (
                <IoChevronDownOutline size={20} className="flex-shrink-0" />
              )}
            </button>
            {openIndex === startIndex + idx && (
              <div>
                {htmlParser(faq.answer, {
                  classNames: {
                    p: "text-neutral-800 animate-fade-in mt-2",
                  },
                })}
              </div>
            )}
          </div>
        ))}

        {/* Pagination Controls - Only show if more than 6 FAQs */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <button
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
              className={`h-10 w-10 rounded-full flex items-center justify-center text-sm transition-colors ${
                currentPage === 1
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-gray-100 text-neutral-1100 hover:border-blue-300"
              }`}
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>

            {/* Total FAQ Count */}
            <div className="text-sm text-gray-600">
              <span className="font-bold">
                {startIndex + 1}-{Math.min(endIndex, faqs.length)}
              </span>{" "}
              of <span className="font-bold">{faqs.length}</span> {sectionTitle}
            </div>

            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className={`h-10 w-10 rounded-full flex items-center justify-center text-sm transition-colors ${
                currentPage === totalPages
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-gray-100 text-neutral-1100 hover:border-blue-300"
              }`}
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    </SectionContainerLarge>
  );
};

export default FAQs;
