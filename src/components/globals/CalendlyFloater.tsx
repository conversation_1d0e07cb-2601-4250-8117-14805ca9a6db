import React from "react";
import { FaPhone } from "react-icons/fa";
import { OpenCalendlyPopup } from "@/utils/calendlyFunction";

const CalendlyFloater: React.FC = () => {


  const handlePhoneClick = () => {
    OpenCalendlyPopup();
  };

  return (
    <div className="fixed bottom-24 right-6 z-50">
      <button
        onClick={handlePhoneClick}
        className="w-12 h-12 bg-primary-800 hover:bg-primary-900 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 ease-in-out transform hover:scale-110"
        aria-label="Call us"
      >
        <FaPhone size={20} />
      </button>
    </div>
  );
};

export default CalendlyFloater;
