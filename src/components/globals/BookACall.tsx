// "use client";

// const BookACallBtn = ({
//   onClick,
//   bgColor = "bg-primary-2 text-white",
// }: {
//   onClick?: () => void;
//   bgColor?: string;
// }) => {
//   // function handleClick() {
//   //   // @ts-ignore
//   //   window.Calendly.initPopupWidget({
//   //     url: "https://calendly.com/oneassure/30min?year=2024",
//   //   });
//   //   return false;
//   // }
//   return (
//     <>
//       <button
//         onClick={onClick ? () => onClick() : () => {}}
//         id="calendly-inline-widget"
//         className={`py-3 px-8 rounded-full cursor-pointer font-generalSans text-[16px]/[24px] font-semibold ${bgColor}`}
//       >
//         Book a Call
//       </button>
//     </>
//   );
// };

// export default BookACallBtn;

// "use client";

// import Script from "next/script";

// const BookACallBtn = ({
//   onClick,
//   bgColor = "bg-primary-2 text-white",
// }: {
//   onClick?: () => void;
//   bgColor?: string;
// }) => {
//   function handleClick() {
//     window.Calendly.initPopupWidget({
//       url: "https://calendly.com/oneassure/30min?year=2024",
//     });
//     return false;
//   }

//   // @ts-ignore
//   function isCalendlyEvent(e) {
//     return (
//       e.origin === "https://calendly.com" &&
//       e.data.event &&
//       e.data.event.indexOf("calendly.") === 0
//     );
//   }

//   if (typeof window !== "undefined") {
//     window.addEventListener("message", function (e) {
//       if (isCalendlyEvent(e)) {
//         /* Example to get the name of the event */
//         console.log("Event name:", e.data.event);

//         /* Example to get the payload of the event */
//         console.log("Event details:", e.data.payload);
//       }
//     });
//   }

//   return (
//     <>
//       <Script
//         src="https://assets.calendly.com/assets/external/widget.js async"
//         type="text/javascript"
//         async
//       />
//       <link
//         href="https://assets.calendly.com/assets/external/widget.css"
//         rel="stylesheet"
//       />
//       <button
//         onClick={handleClick}
//         id="calendly-inline-widget"
//         className="py-3 px-6 md:px-8 bg-blue600 rounded-lg text-white text-base font-semibold border-2 border-blue600 cursor-pointer"
//       >
//         Book a Call
//       </button>
//     </>
//   );
// };

// export default BookACallBtn;

"use client";

import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { forwardRef, useEffect, useRef } from "react";
import { IoCall } from "react-icons/io5";
import { useSessionStorage } from "usehooks-ts";
import { usePathname } from "next/navigation";

const BookACallBtn = forwardRef(function BookACallBtn(
  {
    onClick,
    className,
    icon = false,
    arrow = false,
    rightIcon = false,
    label = "Book a Call",
    utm,
    ...props
  }: {
    onClick?: () => void;
    className?: string;
    label?: string;
    icon?: boolean;
    arrow?: boolean;
    rightIcon?: boolean;
    utm?: string;
    [key: string]: any;
  },
  ref
) {
  const pathname = usePathname();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", "");
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", "");
  const [utm_campaign, setUtmCampaign] = useSessionStorage("utm_campaign", "");
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", "");
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", "");

  useEffect(() => {
    function isCalendlyEvent(e: MessageEvent) {
      return (
        e.origin === "https://calendly.com" &&
        e.data.event &&
        e.data.event.indexOf("calendly.") === 0
      );
    }

    function handleCalendlyMessage(e: MessageEvent) {
      if (isCalendlyEvent(e)) {
        // console.log("Event name:", e.data.event);
        // console.log("Event details:", e.data.payload);
      }
    }

    if (typeof window !== "undefined") {
      window.addEventListener("message", handleCalendlyMessage);
    }

    return () => {
      window.removeEventListener("message", handleCalendlyMessage);
    };
  }, []);

  function handleClick() {
    const script = document.createElement("script");
    script.src = "https://assets.calendly.com/assets/external/widget.js";
    script.async = true;
    document.head.appendChild(script);

    const link = document.createElement("link");
    link.href = "https://assets.calendly.com/assets/external/widget.css";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    // if (window.Calendly) {
    //   window.Calendly.initPopupWidget({
    //     url: "https://calendly.com/oneassure/30min?year=2024",
    //   });
    // }

    script.onload = function () {
      if (window.Calendly) {
        // Get the pathname without domain
        const currentPath = pathname;
        // If utm_medium exists in session, append it to pathname
        const mediumParam = utm_medium
          ? `${currentPath}_${utm_medium}`
          : currentPath;

        window.Calendly.initPopupWidget({
          url: `https://calendly.com/oneassure/30min?year=2024&&utm_source=${utm_source}&utm_medium=${mediumParam}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`,
        });
      }
    };
    return false;
  }

  return (
    <button
      onClick={handleClick}
      id="calendly-inline-widget"
      className={`font-medium cursor-pointer text-base md:text-2xl ${className}`}
      {...props}
      // @ts-ignore
      ref={ref}
    >
      {icon && (
        <div className="bg-secondary-blue-2 p-2 rounded-full">
          <IoCall className="text-xl text-white" />
        </div>
      )}
      {label}
      {arrow && <ArrowRightIcon className="h-5 md:h-7" />}
      {rightIcon && (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clipRule="evenodd"
          />
        </svg>
      )}
    </button>
  );
});

export default BookACallBtn;
