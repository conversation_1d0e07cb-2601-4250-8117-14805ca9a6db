"use client";

import { useEffect } from "react";
import { ArrowUpRightIcon } from "@heroicons/react/24/solid";

const BookACallMobileBtn = ({
  onClick,
  bgColor,
}: {
  onClick?: () => void;
  bgColor: string;
}) => {
  useEffect(() => {
    function isCalendlyEvent(e: MessageEvent) {
      return (
        e.origin === "https://calendly.com" &&
        e.data.event &&
        e.data.event.indexOf("calendly.") === 0
      );
    }

    function handleCalendlyMessage(e: MessageEvent) {
      if (isCalendlyEvent(e)) {
        console.log("Event name:", e.data.event);
        console.log("Event details:", e.data.payload);
      }
    }

    if (typeof window !== "undefined") {
      window.addEventListener("message", handleCalendlyMessage);
    }

    return () => {
      window.removeEventListener("message", handleCalendlyMessage);
    };
  }, []);

  function handleClick() {
    const script = document.createElement("script");
    script.src = "https://assets.calendly.com/assets/external/widget.js";
    script.async = true;
    document.head.appendChild(script);

    const link = document.createElement("link");
    link.href = "https://assets.calendly.com/assets/external/widget.css";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    if (window.Calendly) {
      window.Calendly.initPopupWidget({
        url: "https://calendly.com/oneassure/30min?year=2024",
      });
    }

    return false;
  }

  return (
    <button
      onClick={handleClick}
      id="calendly-inline-widget"
      className={`p-3 font-semibold cursor-pointer ${bgColor}`}
    >
      <ArrowUpRightIcon className={`w-6 h-6 text-ntrl-black font-semibold`} />
    </button>
  );
};

export default BookACallMobileBtn;
