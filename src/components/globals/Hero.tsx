"use client";
import Image from "next/image";
import Link from "next/link";
import { HealthVariantHero } from "../HealthInsurance/types";
import { VariantCompany } from "./types";
import CircularProgress from "./Products/CircularProgress";
import { TermVariantHero } from "../LifeInsurance/types";
const Hero: React.FC<{
  name?: string;
  hero: HealthVariantHero | TermVariantHero;
  company: VariantCompany;
  category: string;
  rating?:
    | {
        id?: number | string;
        coverage: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        claimSettlement: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        hospitalNetwork: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        coPayment: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        waitingPeriods: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        noClaimBonus: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
      }
    | undefined;
}> = (props) => {
  let totalScore = 0;
  if (props.rating) {
    totalScore =
      props.rating.coverage.score +
      props.rating.claimSettlement.score +
      props.rating.hospitalNetwork.score +
      props.rating.coPayment.score +
      props.rating.waitingPeriods.score +
      props.rating.noClaimBonus.score;
  }
  return (
    <div
      className={`rounded-3xl pt-5 md:py-7 pb-6 md:px-12 bg-white ${
        props.category === "health" ? "md:pb-12" : ""
      }`}
    >
      <div
        className={`flex ${
          props.category === "health" ? "gap-7" : "gap-10 md:mb-2"
        } justify-between`}
      >
        <div className={`flex flex-col w-full`}>
          <div className=" flex items-start md:items-center gap-2.5 md:gap-4">
            {/* Logo */}
            <div className="w-24 h-24 mb-2 relative">
              <Image
                src={
                  props.company.logo.data
                    ? props.company.logo.data.attributes.url
                    : ""
                }
                fill={true}
                style={{ objectFit: "contain" }}
                alt={props.company.name}
              />
            </div>

            {/* Name of the company & portability status */}
            <div className="">
              <h1 className="text-[28px]/[30px] md:text-[40px]/[48px] font-medium mb-2 md:mb-3">
                {props.hero?.title || props.name}
              </h1>
              <h2 className="text-[16px]/[20px] md:text-[24px]/[20px] text-ntrl-black inline-block rounded-xl hover:underline hover:cursor-pointer">
                <Link
                  href={`/${props.category}-insurance/${props.company.slug}`}
                >
                  {props.company.name}
                </Link>
              </h2>
            </div>
          </div>

          {props.category === "health" ? (
            <div className="flex md:block gap-3 mt-3 md:mt-auto">
              {props.rating && (
                <div className="flex md:hidden border-[0.5px] border-blue-5 flex-col items-center justify-end text-[36px]/[40px] p-2 rounded-3xl w-[45%]">
                  <CircularProgress
                    percentage={(totalScore / 10) * 100}
                    text={totalScore.toFixed(1).toString()}
                  />
                  <p className="text-center w-full text-[12px]/[16px] md:text-[16px]/5 px-2 py-1 mx-auto rounded-3xl border-[0.5px] border-blue-5 bg-white">
                    OneAssure Rating
                  </p>
                </div>
              )}
              <div className="w-[55%] md:w-full flex flex-col-reverse md:flex-row items-center justify-between gap-3 md:gap-7 mb-1.5 md:mb-2.5">
                <div className="relative flex justify-center text-[26px]/[36px] md:text-[36px]/[40px] w-full md:w-1/2 pt-4 md:pt-[10px] pb-4 rounded-3xl border-[0.5px] border-blue-5 font-poppins">
                  {typeof props.hero?.claimSettlementRatio === "number"
                    ? props.hero.claimSettlementRatio
                    : props.hero?.claimSettlementRatio.value || "NA"}
                  %
                  <div className="absolute text-[12px]/[16px] md:text-[16px]/5 px-[10px] rounded-3xl border-[0.5px] border-blue-5 bg-white top-[3.6rem] md:top-14 font-manrope">
                    Claim Settlement Ratio
                  </div>
                </div>
                <div className="relative flex justify-center text-[26px]/[36px] md:text-[36px]/[40px] w-full md:w-1/2 pt-4 md:pt-[10px] pb-4 rounded-3xl border-[0.5px] border-blue-5 font-poppins">
                  {/* @ts-ignore */}
                  {props.hero?.networkHospitals
                    ? // @ts-ignore
                      props.hero?.networkHospitals.value
                    : "NA"}
                  <div className="absolute text-[12px]/[16px] md:text-[16px]/5 px-[10px] rounded-3xl border-[0.5px] border-blue-5 bg-white top-[3.6rem] md:top-14 font-manrope">
                    Network Hospitals
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex md:block gap-3 mt-3 md:mt-auto">
              {props.rating && (
                <div className="flex md:hidden border-[0.5px] border-blue-5  flex-col items-center justify-end text-[36px]/[40px] p-2 rounded-3xl w-full">
                  <CircularProgress
                    percentage={(totalScore / 10) * 100}
                    text={totalScore.toFixed(1).toString()}
                  />
                  <p className="text-center w-full text-[12px]/[16px] md:text-[16px]/5 px-2 py-1 mx-auto rounded-3xl border-[0.5px] border-blue-5 bg-white">
                    OneAssure Rating
                  </p>
                </div>
              )}
              <div className="md:mt-9 gap-3 relative flex md:flex-row flex-col items-center md:justify-center justify-end text-[36px]/[40px] md:text-[36px]/[40px] w-full py-[10px] rounded-3xl border-[0.5px] border-blue-5 font-poppins">
                <div className="hidden md:block text-[24px]/[32px] pr-14 font-manrope ">
                  Claim Settlement Ratio
                </div>
                {typeof props.hero?.claimSettlementRatio === "number"
                  ? props.hero?.claimSettlementRatio
                  : props.hero?.claimSettlementRatio.value || "NA"}
                %
                <div className="md:hidden text-center py-2.5 mx-2.5 block text-[12px]/[16px] md:text-[16px]/5 px-[10px] rounded-3xl bg-white font-manrope">
                  Claim Settlement Ratio
                </div>
              </div>
            </div>
          )}
        </div>
        {props.rating && (
          <div className="hidden md:flex border-[0.5px] border-blue-5 flex-col items-center justify-end text-[36px]/[40px]  pb-3 px-3 rounded-3xl w-1/4">
            <CircularProgress
              percentage={(totalScore / 10) * 100}
              text={totalScore.toFixed(1).toString()}
            />
            <p className="w-full text-center text-[12px]/[16px] md:text-[16px]/5 px-2 py-1 rounded-3xl border-[0.5px] border-blue-5 bg-white">
              OneAssure Rating
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Hero;
