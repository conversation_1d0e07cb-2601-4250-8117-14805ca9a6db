import NavigationButton from "../NavigationButton";

export const Pagination = ({items, itemsPerPage, currentPage, onPageChange, itemName}:{
    items: any[];
    itemsPerPage: number;
    currentPage: number;
    onPageChange: (page: number) => void;
    itemName: string;
}) => {
  // Calculate pagination
  const totalPages = Math.ceil(items.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  const handlePreviousPage = () => {
    onPageChange(Math.max(currentPage - 1, 1));
  };

  const handleNextPage = () => {
    onPageChange(Math.min(currentPage + 1, totalPages));
  };

  return (
      <div className="flex items-center justify-between mt-8">
        <NavigationButton onClick={handlePreviousPage} disabled={currentPage === 1} direction="prev" />

        {/* Comparison Count */}
        <div className="text-sm text-neutral-800">
          <span className="font-bold">
            {startIndex + 1}-{Math.min(endIndex, items.length)}
          </span>{" "}
          of <span className="font-bold">{items.length}</span> {itemName}
        </div>

        <NavigationButton onClick={handleNextPage} disabled={currentPage === totalPages} direction="next" />
      </div>
  );
};
