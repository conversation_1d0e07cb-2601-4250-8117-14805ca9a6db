import Breadcrumb from "./Breadcrumb";
import SocialShareButtons from "./SocialShareButtons";

const PageTopBar = ({
  breadcrumbPath,
  fullUrl,
}: {
  breadcrumbPath: string[];
  fullUrl: string;
}) => {
  return (
    <div className="w-full flex flex-col md:flex-row items-center md:justify-between md:mb-6">
      {/* Desktop: Original layout */}
      <div className="hidden md:block">
        <Breadcrumb path={breadcrumbPath} fullUrl={fullUrl} />
      </div>
      <div className="hidden md:block">
        <SocialShareButtons />
      </div>
    </div>
  );
};

export default PageTopBar;
