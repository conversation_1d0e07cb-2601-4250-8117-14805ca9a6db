
// Individual item types for the API response
type ContactUsCard = {
    id: string;
    title: string;
    value: string;
    icon_url: string;
}

// Main API response type
export type ContactUsAPIResponse = {
    id: string;
    title: string;
    subtitle: string;   
    contact_us_page_cards: ContactUsCard[];
}

export function transformData(apiResponse: ContactUsAPIResponse): any {
    return {
        id: apiResponse.id,
        hero:{
            title: apiResponse.title,
            subtitle: apiResponse.subtitle,
        },
        contactCards: apiResponse.contact_us_page_cards.map((card) => ({
            id: card.id,
            title: card.title,
            value: card.value,
            icon: {
                data: {
                    id: card.id,
                    attributes: {
                        url: card.icon_url,
                    },
                },
            },
        })),
    }
}