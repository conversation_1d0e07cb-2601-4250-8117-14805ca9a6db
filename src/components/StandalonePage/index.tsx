"use client";
import { StandalonePageData } from "./dto";
import ExpertReview from "../Insurer/component/ExpertReview";
import PlansSection from "../globals/DSComponentsV0/PlansSection";
import Testimonial from "@/components/globals/Testimonial";
import CardContainer from "./components/CardContainer";
import InclusionExclusionSection from "./components/InclusionExclusionSection";
import ClaimTypes from "../Insurer/component/ClaimTypes";
import RenewalTypes from "../Insurer/component/RenewalTypes";
import CategoryCards from "../globals/CategoryCards";
import LeadForm from "../globals/LeadForm";
import FAQs from "../globals/AccordianSection";
import GoToTopFloater from "../globals/GoToTopFloater";
import CalendlyFloater from "../globals/CalendlyFloater";
import SharePageFloater from "../globals/SharePageFloater";
import InsurerPlan from "../globals/InsurerPlan";
import RelatedBlogs from "../globals/RelatedBlogs";
import PageNavigation from "../globals/PageNavigation";
import { useState } from "react";
import HeroSection from "@/components/globals/DSComponentsV0/HeroSection";


const Standalone = ({
  data,
  allInsurerData,
  blogData,
}: {
  data: StandalonePageData;
  allInsurerData?: any[];
  blogData?: {
    heading: string;
    blogs: Array<{
      title: string;
      date: string;
      author: string;
      description: string;
      imageUrl: string;
      url: string;
    }>;
  };
}) => {
  const [activeTab, setActiveTab] = useState("expert-review");
  if (!data) {
    return null;
  }
  return (
    <>
      <HeroSection
        name={data.heroSection.title}
        description={data.heroSection.description}
        pill={data.heroSection.title}
        slug={data.slug}
        breadcrumbPath={data.heroSection.breadcrumbPath}
        stats={data.heroSection.stats}
      />
      <PageNavigation
        activeTab={data.pageNavigationSection.activeTab}
        setActiveTab={setActiveTab}
        tabs={data.pageNavigationSection.tabs}
      />

      {/* Verdict Section */}
      {data.verdictSection &&
        data.verdictSection.whatWeLike &&
        data.verdictSection.whatWeLike.points.length > 0 &&
        data.verdictSection.AreasOfImprovement &&
        data.verdictSection.AreasOfImprovement.points.length > 0 && (
          <ExpertReview
            pill={data.verdictSection.pill}
            heading={data.verdictSection.title}
            subheading={data.verdictSection.description}
            whatWeLike={data.verdictSection.whatWeLike}
            AreasOfImprovement={data.verdictSection.AreasOfImprovement}
            verdict={data.verdictSection.verdict}
          />
        )}

      {/* Plans Section */}
      {data.plansSection &&
        data.plansSection.plans &&
        data.plansSection.plans.length > 0 && (
          <PlansSection
            heading={data.plansSection.title}
            subheading={data.plansSection.description}
            pill={data.plansSection.pill}
            plans={data.plansSection.plans}
            id="plans"
          />
        )}

      {/* Testimonial Section */}
      {data.testimonialSection &&
        data.testimonialSection.points &&
        data.testimonialSection.points.length > 0 && (
          <Testimonial
            testimonials={data.testimonialSection.points}
            sectionHeaderProps={{
              pill: data.testimonialSection.pill,
              heading: data.testimonialSection.title,
              subheading: data.testimonialSection.description,
            }}
            pill={data.testimonialSection.pill}
          />
        )}

      {/* Why Choose This Plan */}
      {data.whyPlansSection &&
        data.whyPlansSection.points &&
        data.whyPlansSection.points.length > 0 && (
          <CardContainer
            heading={data.whyPlansSection.title}
            subheading={data.whyPlansSection.description}
            pill={data.whyPlansSection.pill}
            card_content={data.whyPlansSection.points}
            grid_cols={3}
            id="why-choose-this-plan"
          />
        )}

      {/* Benefits Of This Plan */}
      {data.benefitsSection &&
        data.benefitsSection.points &&
        data.benefitsSection.points.length > 0 && (
          <CardContainer
            heading={data.benefitsSection.title}
            subheading={data.benefitsSection.description}
            pill={data.benefitsSection.pill}
            card_content={data.benefitsSection.points}
            grid_cols={3}
            id="benefits-of-this-plan"
          />
        )}

      {/* Inclusions And Exclusions */}
      {data.inclusionSection &&
        ((data.inclusionSection.inclusions &&
          data.inclusionSection.inclusions.points.length > 0) ||
          (data.inclusionSection.exclusions &&
            data.inclusionSection.exclusions.points.length > 0)) && (
          <InclusionExclusionSection
            heading={data.inclusionSection.title}
            subheading={data.inclusionSection.description}
            pill={data.inclusionSection.pill}
            inclusions={data.inclusionSection.inclusions}
            exclusions={data.inclusionSection.exclusions}
            id="inclusions-and-exclusions"
          />
        )}

      {/* What To Look For */}
      {data.whatToLookForSection &&
        data.whatToLookForSection.points &&
        data.whatToLookForSection.points.length > 0 && (
          <CardContainer
            heading={data.whatToLookForSection.title}
            subheading={data.whatToLookForSection.description}
            pill={data.whatToLookForSection.pill}
            card_content={data.whatToLookForSection.points}
            grid_cols={4}
            parserProps={
              {
                classNames: {
                  p: "text-neutral-900",
                },
              }
            }
            id="what-to-look-for"
          />
        )}

      {/* Key Factors */}
      {data.keyFactorsSection &&
        data.keyFactorsSection.points &&
        data.keyFactorsSection.points.length > 0 && (
          <CardContainer
            heading={data.keyFactorsSection.title}
            subheading={data.keyFactorsSection.description}
            pill={data.keyFactorsSection.pill}
            card_content={data.keyFactorsSection.points}
            grid_cols={4}
            parserProps={
              {
                classNames: {
                  p: "text-neutral-900",
                },
              }
            }
            id="key-factors"
          />
        )}

      {/* Tax Advantage */}
      {data.taxAdvantageSection &&
        data.taxAdvantageSection.points &&
        data.taxAdvantageSection.points.length > 0 && (
          <CardContainer
            heading={data.taxAdvantageSection.title}
            subheading={data.taxAdvantageSection.description}
            pill={data.taxAdvantageSection.pill}
            card_content={data.taxAdvantageSection.points}
            grid_cols={4}
            parserProps={
              {
                classNames: {
                  p: "text-neutral-900",
                },
              }
            }
            id="tax-advantage"
          />
        )}

      {/* Documents Required */}
      {data.documentsSection &&
        data.documentsSection.points &&
        data.documentsSection.points.length > 0 && (
          <CardContainer
            heading={data.documentsSection.title}
            subheading={data.documentsSection.description}
            pill={data.documentsSection.pill}
            card_content={data.documentsSection.points}
            grid_cols={4}
            parserProps={
              {
                classNames: {
                  p: "text-neutral-900",
                },
              }
            }
            id="documents-required"
          />
        )}

      {/* Claim Settlement Process */}
      {data.claimSettlementSection &&
        data.claimSettlementSection.settlements &&
        data.claimSettlementSection.settlements.length > 0 && (
          <ClaimTypes
            heading={data.claimSettlementSection.title}
            subheading={data.claimSettlementSection.description}
            pill={data.claimSettlementSection.pill}
            claimSettlements={data.claimSettlementSection.settlements}
          />
        )}

      {/* Renewal Process */}
      {data.renewalSection &&
        data.renewalSection.renewalSteps &&
        data.renewalSection.renewalSteps.length > 0 && (
          <RenewalTypes
            heading={data.renewalSection.title}
            subheading={data.renewalSection.description}
            pill={data.renewalSection.pill}
            renewalSteps={data.renewalSection.renewalSteps}
          />
        )}

      {/* Insurance Category */}
      {data.insuranceCategorySection && (
          <CategoryCards
            pill={data.insuranceCategorySection.pill}
            heading={data.insuranceCategorySection.heading}
            categories={data.insuranceCategorySection.featuresComparisonData}
            id="insurance-category"
          />
        )}

      {/* Lead Form */}
      <LeadForm
        pill={data.heroSection.title}
        title="Still Confused? Get Expert Guidance"
        description="Our insurance experts can help you compare plans and find the best coverage for your needs"
      />

      {/* FAQ Section */}
      {data.faqSection &&
        data.faqSection.faqs &&
        data.faqSection.faqs.length > 0 && (
          <FAQs
            pill={data.faqSection.pill}
            heading={data.faqSection.title}
            subheading={data.faqSection.description}
            faqs={data.faqSection.faqs}
            id="faqs"
          />
        )}

      {/* Insurer Plans */}
      {allInsurerData && allInsurerData.length > 0 && (
        <InsurerPlan allInsurerData={allInsurerData} />
      )}
      {/* Related Blogs */}
      {blogData && blogData.blogs.length > 0 && (
        <RelatedBlogs blogData={blogData} />
      )}
      <GoToTopFloater />
      <CalendlyFloater />
      <SharePageFloater />
    </>
  );
};

export default Standalone;
