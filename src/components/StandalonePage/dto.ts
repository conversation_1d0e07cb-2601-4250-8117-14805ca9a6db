import { StandAloneApiResponse } from "@/components/StandalonePage/types";
import { featuresComparisonData } from "@/components/Compare/data/featuresComparisonData";


const transformStandalonePageData = (pageData: StandAloneApiResponse) => {
  if (!pageData) {
    return null;
  }
  const HeroSection = {
    pill: pageData.pill_content,
    title: pageData.hero_title,
    description: pageData.hero_description,
    image: pageData.hero_image_url,
    breadcrumbPath: [
      { name: "OneAssure", url: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
      {
        name: pageData.hero_title,
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${pageData.slug}/sp/${pageData.id}`,
      },
    ],
    // stats: [
    //   {
    //     id: 0,
    //     title: "Claim Settlement Ratio",
    //     value: 90,
    //     suffix: "%",
    //     prefix: "Claim Settlement Ratio",
    //   },
    //   {
    //     id: 1,
    //     title: "Network Hospitals",
    //     value: 1000,
    //     suffix: "+",
    //     prefix: "Network Hospitals",
    //   },
    //   {
    //     id: 2,
    //     title: "Overall Rating",
    //     value: 4.5,
    //     suffix: "/5",
    //     prefix: "Overall Rating",
    //   },
    // ],
    stats: pageData.standalone_hero_cards.map((card) => {
      return {
        id: card.id,
        title: card.description,
        value: card.title,
        suffix: "",
        prefix: "",
        icon: card.icon_url,
      };
    }),
  };
  const BenefitsSection = {
    pill: pageData.standalone_benefits_section?.pill_content,
    title: pageData.standalone_benefits_section?.section_title,
    description: pageData.standalone_benefits_section?.section_description,
    points:
      pageData.standalone_benefits_section?.standalone_benefits_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon_url: point.icon_url || "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a0773-54bb-7c6e-ad9e-ce07e64f7569/Group 1000001551.svg",
          };
        }
      ),
  };
  const ClaimSettlementSection = {
    pill: pageData.standalone_claim_settlement_section?.pill_content,
    title: pageData.standalone_claim_settlement_section?.section_title,
    description:
      pageData.standalone_claim_settlement_section?.section_description,
    settlements:
      pageData.standalone_claim_settlement_section?.standalone_claim_settlement_types.map(
        (type) => {
          return {
            id: type.id,
            title: type.title,
            types: type.standalone_claim_settlement_steps.map((step) => {
              return {
                id: step.id,
                title: step.title,
                description: step.description,
              };
            }),
          };
        }
      ),
  };
  const DocumentsSection = {
    pill: pageData.standalone_documents_section?.pill_content,
    title: pageData.standalone_documents_section?.section_title,
    description: pageData.standalone_documents_section?.section_description,
    points:
      pageData.standalone_documents_section?.standalone_documents_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon: point.icon_url || "",
          };
        }
      ),
  };
  const FAQSection = {
    pill: pageData.standalone_faq_section?.pill_content,
    title: pageData.standalone_faq_section?.section_title,
    description: pageData.standalone_faq_section?.section_description,
    faqs: pageData.standalone_faq_section?.standalone_faq_section_points.map(
      (faq) => {
        return {
          id: faq.id,
          question: faq.question,
          answer: faq.answer,
        };
      }
    ),
  };

  const inclusionSection= pageData.standalone_inclusion_section?.standalone_inclusion_section_points.find(
    (point) => point.type === "inclusion"
  );
  const exclusionSection= pageData.standalone_inclusion_section?.standalone_inclusion_section_points.find(
    (point) => point.type === "exclusion"
  );
  const InclusionSection = {
    pill: pageData.standalone_inclusion_section?.pill_content,
    title: pageData.standalone_inclusion_section?.section_title,
    description: pageData.standalone_inclusion_section?.section_description,
    inclusions: {
      title: "Inclusions",
      points: inclusionSection?.points || [],
    },
    exclusions: {
      title: "Exclusions",
      points: exclusionSection?.points || [],
    },
  };
  const InsuranceCategorySection = {
    pill: pageData.hero_title || "Insurance Company",
    heading: "Related Health Insurance Plans",
    featuresComparisonData: featuresComparisonData.plans,
  };
  const KeyFactorsSection = {
    pill: pageData.standalone_key_factors_section?.pill_content,
    title: pageData.standalone_key_factors_section?.section_title,
    description: pageData.standalone_key_factors_section?.section_description,
    points:
      pageData.standalone_key_factors_section?.standalone_key_factors_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon: point.icon_url || "",
          };
        }
      ),
  };
  const PlansSection = {
    pill: pageData.standalone_plans_section?.pill_content,
    title: pageData.standalone_plans_section?.section_title,
    description: pageData.standalone_plans_section?.section_description,
    plans:
      pageData.standalone_plans_section?.standalone_plans_section_plans.map(
        (plan) => {
          return {
            logo_url: plan.health_product_variant.product.insurer.logo_url,
            plan_title: `${plan.health_product_variant.product.insurer.name} ${plan.health_product_variant.variant_name}`,
            redirect_url: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${plan.health_product_variant.product.insurer.temp_slug}/${plan.health_product_variant.temp_slug}`,
          };
        }
      ),
  };
  const RenewalSection = {
    pill: pageData.standalone_renewal_section?.pill_content,
    title: pageData.standalone_renewal_section?.section_title,
    description: pageData.standalone_renewal_section?.section_description,
    renewalSteps:
      pageData.standalone_renewal_section?.standalone_renewal_types.map(
        (type) => {
          return {
            id: type.id,
            title: type.title,
            type: type.type,
            types: type.standalone_renewal_steps.map((step) => {
              return {
                id: step.id,
                title: step.title,
                description: step.description,
              };
            }),
          };
        }
      ),
  };
  const TaxAdvantageSection = {
    pill: pageData.standalone_tax_advantage_section?.pill_content,
    title: pageData.standalone_tax_advantage_section?.section_title,
    description: pageData.standalone_tax_advantage_section?.section_description,
    points:
      pageData.standalone_tax_advantage_section?.standalone_tax_advantage_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon: point.icon_url || "",
          };
        }
      ),
  };
  const WhyPlansSection = {
    pill: pageData.standalone_why_plans_section?.pill_content,
    title: pageData.standalone_why_plans_section?.section_title,
    description: pageData.standalone_why_plans_section?.section_description,
    points:
      pageData.standalone_why_plans_section?.standalone_why_plans_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon_url: point.icon_url || "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a0773-54bb-7c6e-ad9e-ce07e64f7569/Group 1000001551.svg",
          };
        }
      ),
  };
  const WhatToLookForSection = {
    pill: pageData.standalone_what_to_look_for_section?.pill_content,
    title: pageData.standalone_what_to_look_for_section?.section_title,
    description:
      pageData.standalone_what_to_look_for_section?.section_description,
    points:
      pageData.standalone_what_to_look_for_section?.standalone_what_to_look_for_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon: point.icon_url || "",
          };
        }
      ),
  };
  const TestimonialSection = {
    pill: pageData.standalone_testimonial_section?.pill_content,
    title: pageData.standalone_testimonial_section?.section_title,
    description: pageData.standalone_testimonial_section?.section_description,
    points:
      pageData.standalone_testimonial_section?.standalone_testimonial_section_points.map(
        (point) => {
          return {
            id: point.id,
            name: point.title,
            content: point.description,
          };
        }
      ),
  };

  const whatWeLike = pageData.standalone_verdict_section?.standalone_verdict_section_pros_cons.find(
    (point) => point.type === "pro"
  );
  const AreasOfImprovement = pageData.standalone_verdict_section?.standalone_verdict_section_pros_cons.find(
    (point) => point.type === "con"
  );
  const VerdictSection = {
    pill: pageData.standalone_verdict_section?.pill_content,
    title: pageData.standalone_verdict_section?.section_title,
    description: pageData.standalone_verdict_section?.section_description,
    verdict: pageData.standalone_verdict_section?.verdict,
    whatWeLike: {
      heading: "Pros",
      points: whatWeLike?.points || [],
    },
    AreasOfImprovement: {
      heading: "Cons",
      points: AreasOfImprovement?.points || [],
    },
  };

  const PageNavigationSection = {
    activeTab: "expert-review",
    tabs: [
      { label: "Verdict", id: "expert-review" },
      { label: "Testimonials", id: "testimonials" },
      { label: "What's Included", id: "inclusions-and-exclusions" },
      { label: "Key Factors", id: "key-factors" },
      { label: "Claim Settlement Process", id: "claim-settlement" },
      { label: "Renewal Process", id: "renewal-process" },
      { label: "Insurance Category", id: "insurance-category" },
      { label: "FAQs", id: "faqs" },
    ],
  };

  return {
    id: pageData.id,
    slug: pageData.slug,
    heroSection: HeroSection,
    benefitsSection: BenefitsSection,
    claimSettlementSection: ClaimSettlementSection,
    documentsSection: DocumentsSection,
    faqSection: FAQSection,
    inclusionSection: InclusionSection,
    insuranceCategorySection: InsuranceCategorySection,
    keyFactorsSection: KeyFactorsSection,
    plansSection: PlansSection,
    renewalSection: RenewalSection,
    taxAdvantageSection: TaxAdvantageSection,
    whyPlansSection: WhyPlansSection,
    whatToLookForSection: WhatToLookForSection,
    testimonialSection: TestimonialSection,
    verdictSection: VerdictSection,
    pageNavigationSection: PageNavigationSection,
  };
};

export type StandalonePageData = ReturnType<typeof transformStandalonePageData>;

export default transformStandalonePageData;
