export type StandAloneApiResponse = {
    id: string;
    hero_title: string;
    hero_description: string;
    hero_image_url: string;
    pill_content: string;
    slug: string;
    standalone_benefits_section: {
        id: string;
        pill_content: string;
        section_description: string;
        section_title: string;
        standalone_benefits_section_points: {
            id: string;
            title: string;
            description: string;
            icon_url: string;
        }[];
    };
    standalone_claim_settlement_section: {
        id: string;
        section_title: string;
        section_description: string;
        pill_content: string;
        standalone_claim_settlement_types: {
            id: string;
            title: string;
            type: string;
            standalone_claim_settlement_steps: {
                id: string;
                title: string;
                description: string;
            }[];
        }[];
    };
    standalone_documents_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_documents_section_points: {
            id: string;
            title: string;
            description: string;
            icon_url: string;
        }[];
    };
    standalone_faq_section: {
        id: string;
        section_title: string;
        section_description: string;
        pill_content: string;
        standalone_faq_section_points: {
            id: string;
            question: string;
            answer: string;
        }[];
    };
    standalone_inclusion_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_inclusion_section_points: {
            id: string;
            type: string;
            points: string[];
        }[];
    };
    standalone_insurance_category_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_insurance_category_section_cards: {
            id: string;
            most_popular: boolean;
            title: string;
            points: string[];
            icon_url: string;
        }[];
    };
    standalone_key_factors_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_key_factors_section_points: {
            id: string;
            title: string;
            description: string;
            icon_url: string;
        }[];
    };
    standalone_plans_section: {
        id: string;
        section_title: string;
        section_description: string;
        pill_content: string;
        standalone_plans_section_plans:{
            id: string;
            health_product_variant: {
                id: string;
                variant_name: string;
                variant_slug: string;
                temp_slug: string;
                product: {
                    insurer: {
                        id: string;
                        name: string;
                        logo_url: string;
                        slug: string;
                        temp_slug: string;
                    };
                };
            };
        }[];
    };
    standalone_renewal_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_renewal_types: {
            id: string;
            title: string;
            type: string;
            standalone_renewal_steps: {
                id: string;
                title: string;
                description: string;
            }[];
        }[];
    };
    standalone_tax_advantage_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_tax_advantage_section_points: {
            id: string;
            title: string;
            description: string;
            icon_url: string;
        }[];
    };
    standalone_testimonial_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_testimonial_section_points: {
            id: string;
            title: string;
            description: string;
        }[];
    };
    standalone_verdict_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        verdict: string;
        standalone_verdict_section_pros_cons: {
            id: string;
            points: string[];
            title: string;
            type: string;
        }[];
    };
    standalone_what_to_look_for_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_what_to_look_for_section_points: {
            id: string;
            title: string;
            description: string;
            icon_url: string;
        }[];
    };
    standalone_why_plans_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        standalone_why_plans_section_points: {
            id: string;
            title: string;
            description: string;
            icon_url: string;
        }[];
    };
    standalone_hero_cards: {
        id: string;
        title: string;
        description: string;
        icon_url: string;
    }[];
}

