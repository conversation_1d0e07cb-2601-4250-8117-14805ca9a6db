import React, { useState } from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import {
  BodyMedium,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import { Button } from "@/components/UI/Button";
import BMIModal from "./ConsumerPopup";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { FaMale, FaFemale, FaBaby } from "react-icons/fa";
import { htmlParser } from "@/utils/htmlParser";

type BMIPlan = {
  title: string;
  description: string;
  iconKey: "men" | "women" | "children" | string;
  iconBgColor: string;
  button: string;
  features: string[];
  bmiRanges: {
    title: string;
    range: string;
    description: string;
    risks: {
      title: string;
      description: string;
    }[];
    expanded?: boolean;
  }[];
};

type ConsumerProps = {
  plans: BMIPlan[];
};

const Consumer = ({ plans }: ConsumerProps) => {
  const [selectedPlan, setSelectedPlan] = useState<BMIPlan | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Custom renderer that strips HTML tags and styling
  const stripHtmlRenderer = (domNode: any, index: number) => {
    if (domNode.type === "tag" && domNode.name === "span") {
      return domNode.children?.[0]?.data || "";
    }
    if (domNode.type === "tag" && domNode.name === "p") {
      return domNode.children?.[0]?.data || "";
    }
    return undefined;
  };

  const handleClick = (plan: BMIPlan) => {
    setSelectedPlan(plan);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedPlan(null);
  };

  const iconFromKey = (idx?: number) => {
    switch (idx) {
      case 0:
        return {
          icon: <FaMale className="w-6 h-6 text-teal-500" />,
          bgColor: "bg-teal-100",
        };
      case 1:
        return {
          icon: <FaFemale className="w-6 h-6 text-purple-500" />,
          bgColor: "bg-purple-100",
        };
      case 2:
        return {
          icon: <FaBaby className="w-6 h-6 text-tertiary-orange-400" />,
          bgColor: "bg-orange-100",
        };
      default:
        return {
          icon: <FaMale className="w-6 h-6 text-teal-500" />,
          bgColor: "bg-teal-100",
        };
    }
  };

  return (
    <>
      <SectionContainerLarge
        className="w-full flex flex-col items-center !px-0"
        id="bmi-chart"
      >
        {/* Main Title - Centered and Large */}
        <HeadingXLarge
          as="h2"
          className="text-neutral-1100 text-center mb-6 font-semibold px-6 md:px-0"
        >
          BMI Chart for Men, Women and Children
        </HeadingXLarge>

        {/* Desktop Grid Layout */}
        {plans?.length > 0 && (
          <div className="hidden md:grid md:grid-cols-3 gap-8 items-stretch w-full">
            {plans.map((plan, idx) => (
              <div
                key={idx}
                className="relative flex flex-col justify-center items-center rounded-2xl w-full p-4 transition-all duration-300 text-center md:p-6 shadow-md border border-primary-200"
              >
                {/* Icon - Centered at top with colored background */}
                <div className="flex justify-center items-center">
                  <div
                    className={`w-12 h-12 rounded-full ${
                      iconFromKey(idx).bgColor
                    } flex items-center justify-center`}
                  >
                    {iconFromKey(idx).icon}
                  </div>
                </div>

                {/* Title - Bold and centered */}
                <HeadingSmall
                  as="h3"
                  weight="semibold"
                  className="text-neutral-1100 text-center my-3"
                >
                  {plan.title}
                </HeadingSmall>

                {/* Description - Centered text with proper spacing */}
                <div>
                  {htmlParser(plan.description, {
                    classNames: {
                      p: "text-neutral-1100 text-center mb-4",
                    },
                  })}
                </div>

                {/* Button - Full width, centered */}
                <div className="mt-auto">
                  <Button
                    variant="primary"
                    className="w-full"
                    onClick={() => handleClick(plan)}
                  >
                    <BodyMedium className="">{plan.button}</BodyMedium>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Mobile Carousel Layout */}
        {plans?.length > 0 && (
          <div className="md:hidden w-full">
            <MobileCarousel totalSlides={plans.length}>
              {plans.map((plan, idx) => (
                <MobileCarouselItem key={idx}>
                  <div className="relative flex flex-col justify-center items-center rounded-xl w-full p-4 transition-all duration-300 text-center shadow-md border border-primary-300">
                    {/* Icon - Centered at top with colored background */}
                    <div className="flex justify-center items-center">
                      <div
                        className={`w-12 h-12 rounded-full ${
                          iconFromKey(idx).bgColor
                        } flex items-center justify-center`}
                      >
                        {iconFromKey(idx).icon}
                      </div>
                    </div>

                    {/* Title - Bold and centered */}
                    <HeadingSmall
                      as="h3"
                      weight="semibold"
                      className="text-neutral-1100 text-center my-3"
                    >
                      {plan.title}
                    </HeadingSmall>

                    {/* Description - Centered text with proper spacing */}
                    <div>
                      {htmlParser(plan.description, {
                        classNames: {
                          p: "text-neutral-1100 text-center mb-4",
                        },
                      })}
                    </div>

                    {/* Button - Full width, centered */}
                    <div className="mt-auto">
                      <Button
                        variant="primary"
                        className="w-full"
                        onClick={() => handleClick(plan)}
                      >
                        <BodyMedium className="">{plan.button}</BodyMedium>
                      </Button>
                    </div>
                  </div>
                </MobileCarouselItem>
              ))}
            </MobileCarousel>
          </div>
        )}
      </SectionContainerLarge>

      {/* BMI Modal */}
      <BMIModal
        plan={selectedPlan as BMIPlan}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </>
  );
};

export default Consumer;
