import { BodyLarge, BodySmall } from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";
import { useState } from "react";
import { IoClose, IoChevronUp, IoChevronDown } from "react-icons/io5";

// Close icon for modal
const CloseIcon = () => <IoClose className="w-6 h-6" />;

// Chevron icons for collapsible sections
const ChevronUpIcon = () => <IoChevronUp className="w-5 h-5" />;

const ChevronDownIcon = () => <IoChevronDown className="w-5 h-5" />;

type BMIPopupProps = {
  plan: {
    title: string;
    description: string;
    bmiRanges: {
      title: string;
      range: string;
      description: string;
      risks: {
        title: string;
        description: string;
      }[];
      expanded?: boolean;
    }[];
  };
  isOpen: boolean;
  onClose: () => void;
};

// BMI Modal Component
const BMIModal = ({ plan, isOpen, onClose }: BMIPopupProps) => {
  const [expandedSections, setExpandedSections] = useState<number[]>(
    plan?.bmiRanges
      .map((_, index) => index)
      .filter((i) => plan.bmiRanges[i].expanded) || []
  );

  const toggleSection = (index: number) => {
    setExpandedSections((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  if (!isOpen || !plan) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="relative flex items-center justify-center px-4 pt-4 pb-1 md:p-6 w-full">
          <BodyLarge className="text-neutral-1100 font-semibold text-center">
            {plan.title}
          </BodyLarge>
          <button
            onClick={onClose}
            className="absolute right-6 text-neutral-1100 hover:text-neutral-900 transition-colors"
          >
            <CloseIcon />
          </button>
        </div>

        {/* Content */}
        <div className="px-6 pb-6">
          {/* Introduction */}
          <div>
            {htmlParser(plan.description, {
              components: {
                p: BodySmall,
              },
              classNames: {
                p: "text-neutral-800 text-center mb-3",
              },
            })}
          </div>

          {/* BMI Ranges */}
          <div className="space-y-3 md:space-y-4">
            {plan.bmiRanges.map((range, index) => (
              <div key={index} className="border border-primary-200 rounded-lg">
                <button
                  onClick={() => toggleSection(index)}
                  className={`w-full flex items-center justify-between text-left hover:bg-gray-50 transition-colors ${
                    expandedSections.includes(index) ? "px-4 pt-4 pb-2" : "p-4"
                  }`}
                >
                  <BodySmall className="font-medium text-neutral-1100">
                    {range.title} - {range.description}
                  </BodySmall>
                  {expandedSections.includes(index) ? (
                    <div className="text-neutral-800">
                      <ChevronUpIcon />
                    </div>
                  ) : (
                    <div className="text-neutral-800">
                      <ChevronDownIcon />
                    </div>
                  )}
                </button>

                {expandedSections.includes(index) && range.risks.length > 0 && (
                  <div className="px-4 pb-4">
                    <ul className="space-y-3">
                      {range.risks.map((risk, riskIndex) => (
                        <li key={riskIndex} className="flex items-start">
                          <div className="text-neutral-1100 flex items-center gap-1">
                            <BodySmall className="text-neutral-1100">
                              {risk.title}
                            </BodySmall>
                            -
                            <span>
                              {htmlParser(risk.description, {
                                components: {
                                  p: BodySmall,
                                },
                                classNames: {
                                  p: "text-neutral-800",
                                },
                              })}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BMIModal;
