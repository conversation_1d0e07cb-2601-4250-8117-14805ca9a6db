
// Individual item types for the API response
type CareerBenefit = {
    id: string;
    title: string;
    subtitle: string;
    icon_url: string;
}

type CareerJob = {
    id: string;
    job_title: string;
    department: string;
    location: string;
    type: string;
    link: string;
    posted_date: string;
}

type CareerTeamImage = {
    id: string;
    image_url: string;
}

type CareerTestimonial = {
    id: string;
    name: string;
    statement: string;
    background_color: string;
    thumbnail_url: string | null;
}

// Main API response type
export type CareerPageAPIResponse = {
    id: string;
    title: string;
    subtitle: string;
    benefits_title: string;
    testimonials_title: string;
    team_title: string;
    team_subtitle: string;
    job_title: string;
    career_page_benefits: CareerBenefit[];
    career_page_jobs: CareerJob[];
    career_page_team_images: CareerTeamImage[];
    career_page_testimonials: CareerTestimonial[];
}

export function transformData(apiResponse: CareerPageAPIResponse): any {
    return {
        id: apiResponse.id,
        hero: {
            title: apiResponse.title,
            subtitle: apiResponse.subtitle
        },
        benefits: {
            title: apiResponse.benefits_title,
            benefitItems: apiResponse.career_page_benefits.map((benefit, index) => ({
                id: index + 1,
                title: benefit.title,
                subtitle: benefit.subtitle,
                icon: {
                    data: {
                        id: index + 1,
                        attributes: {
                            url: benefit.icon_url
                        }
                    }
                }
            }))
        },
        team: {
            title: apiResponse.team_title,
            subtitle: apiResponse.team_subtitle,
            images: {
                data: apiResponse.career_page_team_images.map((image, index) => ({
                    id: index + 1,
                    attributes: {
                        url: image.image_url
                    }
                }))
            }
        },
        testimonials: {
            title: apiResponse.testimonials_title,
            testimonial: apiResponse.career_page_testimonials.map((testimonial, index) => ({
                id: index + 1,
                name: testimonial.name,
                statement: testimonial.statement,
                backgroundColor: testimonial.background_color,
                thumbnail: testimonial.thumbnail_url ? {
                    data: {
                        id: index + 1,
                        attributes: {
                            url: testimonial.thumbnail_url
                        }
                    }
                } : null
            }))
        },
        job: {
            title: apiResponse.job_title,
            jobItems: apiResponse.career_page_jobs.map((job, index) => ({
                id: index + 1,
                jobTitle: job.job_title,
                department: job.department,
                location: job.location,
                type: job.type,
                link: job.link,
                postedDate: job.posted_date
            }))
        }
    };
}