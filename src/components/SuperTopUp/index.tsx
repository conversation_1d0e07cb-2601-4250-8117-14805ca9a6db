"use client";
import React, { useState } from "react";
import <PERSON> from "@/components/Insurer/component/Hero";
import ExpertReview from "@/components/Insurer/component/ExpertReview";
import PlansSection from "@/components/globals/DSComponentsV0/PlansSection";
import CardContainer from "@/components/StandalonePage/components/CardContainer";
import Testimonial from "@/components/globals/Testimonial";
import CategoryCards from "@/components/globals/CategoryCards";
import ClaimTypes from "@/components/Insurer/component/ClaimTypes";
import LeadForm from "@/components/globals/LeadForm";
import FAQs from "@/components/globals/AccordianSection";
import PageNavigation from "@/components/globals/PageNavigation";
import RelatedBlogs from "@/components/globals/RelatedBlogs";
import InclusionExclusionSection from "@/components/StandalonePage/components/InclusionExclusionSection";
import { SuperTopUpData } from "@/components/SuperTopUp/dto";
import InsurerPlan from "../globals/InsurerPlan";
import RenewalTypes from "../Insurer/component/RenewalTypes";
import AccordianSection from "@/components/globals/AccordianSection";
import HeroSection from "@/components/globals/DSComponentsV0/HeroSection";
import { featuresComparisonData } from "@/components/Compare/data/featuresComparisonData";


const SuperTopUp = ({
  data,
  allInsurerData,
  blogData,
}: {
  data: SuperTopUpData;
  allInsurerData?: any[];
  blogData?: {
    heading: string;
    blogs: Array<{
      title: string;
      date: string;
      author: string;
      description: string;
      imageUrl: string;
      url: string;
    }>;
  };
}) => {
  const [activeTab, setActiveTab] = useState("expert-review");

  if (!data) {
    return null;
  }

  const {
    heroSection,
    verdictSection,
    aboutSection,
    benefitsSection,
    highlightsSection,
    uniqueFeaturesSection,
    eligiblityCriteriaSection,
    factorsToConsiderSection,
    pageNavigationSection,
    inclusionSection,
    insuranceCategorySection,
    renewalSection,
    howToBuySection,
    faqSection,
    claimSettlementSection,
    testimonialSection,
    healthInsurer,
  } = data;

  return (
    <>
      
      <HeroSection
        name={heroSection.title}
        description={heroSection.description}
        image={heroSection.image}
        breadcrumbPath={heroSection.breadcrumbPath}
        stats={heroSection.stats}
      />
      <PageNavigation
        activeTab={pageNavigationSection.activeTab}
        setActiveTab={setActiveTab}
        tabs={pageNavigationSection.tabs}
      />
      {data.verdictSection && (
        <ExpertReview
          pill={verdictSection.pill}
          heading={verdictSection.title}
          whatWeLike={verdictSection.whatWeLike}
          AreasOfImprovement={verdictSection.AreasOfImprovement}
          verdict={verdictSection.verdict}
        />
      )}

      {data.aboutSection &&
        data.aboutSection.cards &&
        data.aboutSection.cards.length > 0 && (
          <CardContainer
            heading={aboutSection.title}
            subheading={aboutSection.description}
            pill={aboutSection.pill}
            card_content={aboutSection.cards}
            grid_cols={2}
            id="about-super-top-up"
          />
        )}
      {data.benefitsSection &&
        data.benefitsSection.cards &&
        data.benefitsSection.cards.length > 0 && (
          <CardContainer
            heading={benefitsSection.title}
            pill={benefitsSection.pill}
            card_content={benefitsSection.cards}
            grid_cols={4}
            id="benefits"
          />
        )}
      {data.testimonialSection &&
        data.testimonialSection.testimonials &&
        data.testimonialSection.testimonials.length > 0 && (
          <Testimonial
            testimonials={testimonialSection.testimonials}
            sectionHeaderProps={testimonialSection.sectionHeaderProps}
            pill={testimonialSection.pill}
          />
        )}
      {data.highlightsSection &&
        data.highlightsSection.cards &&
        data.highlightsSection.cards.length > 0 && (
          <CardContainer
            heading={highlightsSection.title}
            pill={highlightsSection.pill}
            card_content={highlightsSection.cards}
            grid_cols={4}
            id="highlights"
          />
        )}
      {data.uniqueFeaturesSection &&
        data.uniqueFeaturesSection.cards &&
        data.uniqueFeaturesSection.cards.length > 0 && (
          <CardContainer
            heading={uniqueFeaturesSection.title}
            pill={uniqueFeaturesSection.pill}
            card_content={uniqueFeaturesSection.cards}
            grid_cols={4}
            id="unique-features"
          />
        )}
      {data.eligiblityCriteriaSection &&
        data.eligiblityCriteriaSection.cards &&
        data.eligiblityCriteriaSection.cards.length > 0 && (
          <CardContainer
            heading={eligiblityCriteriaSection.title}
            pill={eligiblityCriteriaSection.pill}
            card_content={eligiblityCriteriaSection.cards}
            grid_cols={4}
            id="eligibility"
          />
        )}
      {data.factorsToConsiderSection &&
        data.factorsToConsiderSection.cards &&
        data.factorsToConsiderSection.cards.length > 0 && (
          <CardContainer
            heading={factorsToConsiderSection.title}
            pill={factorsToConsiderSection.pill}
            card_content={factorsToConsiderSection.cards}
            grid_cols={4}
            id="factors-to-consider"
          />
        )}
      {data.inclusionSection &&
        data.inclusionSection.inclusions &&
        data.inclusionSection.inclusions.points.length > 0 && (
          <InclusionExclusionSection
            heading={inclusionSection.title}
            subheading={inclusionSection.description}
            pill={inclusionSection.pill}
            inclusions={inclusionSection.inclusions}
            exclusions={inclusionSection.exclusions}
            id="inclusions-and-exclusions"
          />
        )}
      {data.insuranceCategorySection &&(
          <CategoryCards
            pill={insuranceCategorySection.pill}
            heading={insuranceCategorySection.heading}
            categories={insuranceCategorySection.featuresComparisonData}
            id="insurance-category"
          />
        )}
      {data.claimSettlementSection &&
        data.claimSettlementSection.settlements &&
        data.claimSettlementSection.settlements.length > 0 && (
          <ClaimTypes
            heading={claimSettlementSection.title}
            subheading={""}
            pill={claimSettlementSection.pill}
            claimSettlements={claimSettlementSection.settlements}
          />
        )}

      {/* <ClaimTypes
        heading={renewalSection.title}
        subheading={""}
        pill={renewalSection.pill}
        claimSettlements={renewalSection.renewalSteps}
      /> */}
      {data.renewalSection &&
        data.renewalSection.renewalSteps &&
        data.renewalSection.renewalSteps.length > 0 && (
          <RenewalTypes
            pill={renewalSection.pill}
            heading={renewalSection.title}
            subheading={renewalSection.description}
            renewalSteps={renewalSection.renewalSteps}
          />
        )}

      {data.howToBuySection &&
        data.howToBuySection.steps &&
        data.howToBuySection.steps.length > 0 && (
          <AccordianSection
            pill={howToBuySection.pill}
            heading={howToBuySection.title}
            subheading={howToBuySection.description}
            faqs={howToBuySection.steps}
            sectionTitle="Steps"
            id="how-to-buy"
          />
        )}

      {/* <FAQs
        pill={howToBuySection.pill}
        heading={howToBuySection.title}
        subheading={howToBuySection.description}
        faqs={howToBuySection.steps}
        id="how-to-buy"
      /> */}
      <LeadForm
        pill={data.heroSection.title}
        title="Still Confused? Get Expert Guidance"
        description="Our insurance experts can help you compare plans and find the best coverage for your needs"
      />
      {data.faqSection &&
        data.faqSection.faqs &&
        data.faqSection.faqs.length > 0 && (
          <FAQs
            pill={faqSection.pill}
            heading={faqSection.title}
            subheading={faqSection.description}
            faqs={faqSection.faqs}
            id="faqs"
          />
        )}

      {/* Insurer Plans */}
      {allInsurerData && allInsurerData.length > 0 && (
        <InsurerPlan allInsurerData={allInsurerData} />
      )}
      {/* Related Blogs */}
      {blogData && blogData.blogs.length > 0 && (
        <RelatedBlogs blogData={blogData} />
      )}
    </>
  );
};

export default SuperTopUp;
