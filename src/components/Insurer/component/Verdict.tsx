import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { BodyLarge, HeadingMedium } from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";
import { useState } from "react";

const extractTextFromHTML = (htmlString: string): string => {
  // Check if we're in the browser environment
  if (typeof window === "undefined") {
    // Server-side fallback: simple regex to strip HTML tags
    return htmlString
      .replace(/<[^>]*>/g, " ")
      .replace(/\s+/g, " ")
      .trim();
  }

  const temp = document.createElement("div");
  temp.innerHTML = htmlString;
  return temp.textContent || temp.innerText || "";
};

// Mobile component
const MobileAbout = ({
  verdict,
  heading,
}: {
  verdict: string;
  heading: string;
}) => {
  const [showFullText, setShowFullText] = useState(false);

  const textContent = extractTextFromHTML(verdict);
  const words = textContent.split(/\s+/).filter((word) => word.length > 0);
  const shouldShowSeeMore = words.length > 35;

  const truncatedText =
    shouldShowSeeMore && !showFullText
      ? words.slice(0, 35).join(" ") + "..."
      : verdict;

  const parsedAbout = htmlParser(shouldShowSeeMore && !showFullText ? truncatedText : verdict, {
    components:{
      p: BodyLarge,
      span: BodyLarge,
    },
    classNames: {
      p: "text-neutral-1100 text-justify",
    },
  });

  return (
    <SectionContainerLarge className="!mb-0">
      <div className="text-neutral-1100 text-justify">
        {parsedAbout}
        {shouldShowSeeMore && (
          <button
            onClick={() => setShowFullText(!showFullText)}
            className="text-primary-800 text-base font-semibold hover:underline focus:outline-none mt-2"
          >
            {showFullText ? "See less" : "See more"}
          </button>
        )}
      </div>
    </SectionContainerLarge>
  );
};

// Desktop component with full content
const DesktopAbout = ({
  verdict,
  heading,
}: {
  verdict: string;
  heading: string;
}) => {
  const [showFullText, setShowFullText] = useState(false);

  const textContent = extractTextFromHTML(verdict);
  const words = textContent.split(/\s+/).filter((word) => word.length > 0);
  const shouldShowSeeMore = words.length > 50; // Higher threshold for desktop

  const truncatedText =
    shouldShowSeeMore && !showFullText
      ? words.slice(0, 50).join(" ") + "..."
      : verdict;

  const parsedAbout = htmlParser(shouldShowSeeMore && !showFullText ? truncatedText : verdict, {
    classNames: {
      p: "text-neutral-1100 text-justify",
    },
  });

  return (
    <SectionContainerLarge className="!mb-0">
      <div className="text-neutral-1100 text-justify">
        {parsedAbout}
        {shouldShowSeeMore && (
          <button
            onClick={() => setShowFullText(!showFullText)}
            className="text-primary-800 text-base font-semibold hover:underline focus:outline-none mt-2"
          >
            {showFullText ? "See less" : "See more"}
          </button>
        )}
      </div>
    </SectionContainerLarge>
  );
};
const Verdict = ({
  verdict,
  heading,
}: {
  verdict: string;
  heading: string;
}) => {
  return (
    <div className="bg-primary-100 p-4 border border-primary-300 rounded-xl mx-6 md:mx-0">
      <HeadingMedium
        as="h3"
        className="text-neutral-1100 text-center font-semibold mb-2"
      >
        {heading}
      </HeadingMedium>
       <div className="hidden md:block ">
        <DesktopAbout verdict={verdict} heading={heading}  />
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden ">
        <MobileAbout verdict={verdict} heading={heading}  />
      </div>
    </div>
  );
};

export default Verdict;
