import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import {
  BodyLarge,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";

type DocumentItem = {
  id: string;
  title: string;
  required_documents: string[];
};

type DocumentsProps = {
  title?: string;
  documents: DocumentItem[];
  customerSupport?: {
    email: string;
    number: string;
  };
};

const Documents = ({
  title = "Documents Required",
  documents,
  customerSupport,
}: DocumentsProps) => {

  const DocumentCard = (document: DocumentItem) => {
    return (
      <SectionContainerMedium className="bg-white rounded-xl shadow-sm border border-primary-200 !mb-0 md:!mb-0">
        <div className="flex flex-col gap-1 md:gap-2 !p-2 md:!p-4">
          <HeadingSmall className="text-neutral-1100 text-center" as={"h3"}>
            {document.title}
          </HeadingSmall>
          <div className="text-neutral-1100 font-normal text-center">
            {document.required_documents.map((document: string, index: number) => {
              return <BodyLarge key={index}>{document}</BodyLarge>;
            })}
          </div>
        </div>
      </SectionContainerMedium>
    );
  };

  return (
    <SectionContainerLarge id="documents-required">
      <HeadingXLarge as="h2" className="text-center mb-4 md:mb-6 text-neutral-1100">
        {title}
      </HeadingXLarge>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {documents.map((card, index) => {
          return <DocumentCard key={index} {...card} />;
        })}
      </div>
      {/* {customerSupport && (
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            For assistance: {customerSupport.email} | {customerSupport.number}
          </p>
        </div>
      )} */}
    </SectionContainerLarge>
  );
};

export default Documents;
