import { Button } from "@/components/UI/Button";
import { HeadingMedium } from "@/components/UI/Typography";
import { OpenCalendlyPopup } from "@/utils/calendlyFunction";
import { FaShieldAlt } from "react-icons/fa";
import { htmlParser } from "@/utils/htmlParser";

type InsurerType = {
  id: string;
  title: string;
  description: string;
  button_text: string;
};

const InsurerTypeCard = ({ type }: { type: InsurerType }) => {
  function handleClick() {
    OpenCalendlyPopup();
  }

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex flex-col items-center gap-4 text-center w-full h-full">
      <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
        <FaShieldAlt className="w-6 h-6 text-tertiary-orange-400" />
      </div>
      <HeadingMedium as="h3" className="font-semibold text-neutral-1100">
        {type.title}
      </HeadingMedium>
      <div className="flex-1">
        {htmlParser(type.description, {
          classNames: {
            p: "text-neutral-1100",
          },
        })}
      </div>
      <Button variant="primary" className="mt-2 w-full" onClick={handleClick}>
        <span className="text-white text-sm">{type.button_text}</span>
      </Button>
    </div>
  );
};

export default InsurerTypeCard;
