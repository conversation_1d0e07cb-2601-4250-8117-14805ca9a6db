import { BodyLarge, HeadingMedium } from "@/components/UI/Typography";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import {
  IoMdCheckmarkCircleOutline,
  IoMdCloseCircleOutline,
} from "react-icons/io";

type ExpertReviewListsProps = {
  whatWeLike: {
    heading: string;
    points: string[];
  };
  areasOfImprovement: {
    heading: string;
    points: string[];
  };
}

const ExpertReviewLists: React.FC<ExpertReviewListsProps> = ({
  whatWeLike,
  areasOfImprovement,
}) => {
  const renderCard = (
    title: string,
    points: string[],
    icon: React.ReactNode,
    iconColor: string
  ) => (
    <div className="flex-1 bg-white rounded-xl p-4 md:px-6 md:py-5 gap-4 shadow-sm flex flex-col items-start justify-start border-[1px] border-primary-200 transition-all">
      <HeadingMedium weight="semibold" className="text-neutral-1100">
        {title}
      </HeadingMedium>
      {points.length === 0 ? (
        <BodyLarge className="text-neutral-800">Not available</BodyLarge>
      ) : (
        <ul className="flex flex-col items-start gap-3 w-full">
          {points.map((point, index) => (
            <li key={index} className="flex items-start gap-3 w-full">
              <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center mt-1">
                {icon}
              </div>
              <BodyLarge className="text-neutral-800 flex-1">{point}</BodyLarge>
            </li>
          ))}
        </ul>
      )}
    </div>
  );

  return (
    <>
      {/* Desktop Layout */}
      <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-8 w-full">
        {renderCard(
          whatWeLike.heading,
          whatWeLike.points,
          <IoMdCheckmarkCircleOutline className="text-secondary-400 w-5 h-5" />,
          "text-secondary-400"
        )}
        {renderCard(
          areasOfImprovement.heading,
          areasOfImprovement.points,
          <IoMdCloseCircleOutline className="text-red-400 w-5 h-5" />,
          "text-red-400" 
        )}
      </div>

      {/* Mobile Carousel Layout */}
      <div className="block md:hidden w-full">
        <MobileCarousel totalSlides={2}>
          <MobileCarouselItem>
            {renderCard(
              whatWeLike.heading,
              whatWeLike.points,
              <IoMdCheckmarkCircleOutline className="text-secondary-400 w-5 h-5" />,
              "text-secondary-400"
            )}
          </MobileCarouselItem>
          <MobileCarouselItem>
            {renderCard(
              areasOfImprovement.heading,
              areasOfImprovement.points,
              <IoMdCloseCircleOutline className="text-red-400 w-5 h-5" />,
              "text-red-400"
            )}
          </MobileCarouselItem>
        </MobileCarousel>
      </div>
    </>
  );
};

export default ExpertReviewLists;
