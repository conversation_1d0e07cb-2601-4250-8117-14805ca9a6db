import React, { useState, useEffect, useRef } from "react";

interface MobileCarouselProps {
  children: React.ReactNode;
  totalSlides: number;
  className?: string;
}

const MobileCarousel: React.FC<MobileCarouselProps> = ({
  children,
  totalSlides,
  className = "",
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    const container = scrollContainerRef.current;
    if (container) {
      const cardWidth = container.clientWidth;
      container.scrollTo({
        left: index * cardWidth,
        behavior: "smooth",
      });
    }
  };

  // Handle scroll events for mobile view
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollLeft = container.scrollLeft;
      const cardWidth = container.clientWidth;
      const currentIndex = Math.round(scrollLeft / cardWidth);
      setCurrentSlide(currentIndex);
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className={`w-full ${className}`}>
      {/* Carousel Container */}
      <div className="relative">
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scrollbar-hide scroll-smooth"
          style={{
            scrollbarWidth: "none",
            msOverflowStyle: "none",
          }}
        >
          <div className="flex">
            <div className="w-6 flex-shrink-0"></div>
            <div className="flex gap-4">{children}</div>
            <div className="w-6 flex-shrink-0"></div>
          </div>
        </div>
      </div>

      {/* Pagination Dots */}
      <div className="flex justify-center mt-6 gap-2">
        {Array.from({ length: totalSlides }).map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2 h-2 rounded-full transition-colors duration-200 ${
              index === currentSlide ? "bg-neutral-1100" : "bg-neutral-400"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default MobileCarousel;
