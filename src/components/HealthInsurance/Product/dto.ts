import { HealthVariantData } from "../types";
import { transformedHealthVariantData as transformedHealthVariantDataTypes } from "./types";

export default function dataTransformer(apiData: HealthVariantData) {

  const coverageData = apiData.health_variant_ratings.find(
    (rating) => rating.label === "coverage"
  );
  const coverage = {
    id: coverageData?.id || "",
    title: coverageData?.title || "",
    maxScore: coverageData?.max_score || 0,
    score: coverageData?.score || 0,
  };

  const claimSettlementData = apiData.health_variant_ratings.find(
    (rating) => rating.label === "claimSettlement"
  );
  const claimSettlement = {
    id: claimSettlementData?.id || "",
    title: claimSettlementData?.title || "",
    maxScore: claimSettlementData?.max_score || 0,
    score: claimSettlementData?.score || 0,
  };

  const hospitalNetworkData = apiData.health_variant_ratings.find(
    (rating) => rating.label === "hospitalNetwork"
  );
  const hospitalNetwork = {
    id: hospitalNetworkData?.id || "",
    title: hospitalNetworkData?.title || "",
    maxScore: hospitalNetworkData?.max_score || 0,
    score: hospitalNetworkData?.score || 0,
  };
  
  const coPaymentData = apiData.health_variant_ratings.find(
    (rating) => rating.label === "coPayment"
  );
  const coPayment = {
    id: coPaymentData?.id || "",
    title: coPaymentData?.title || "",
    maxScore: coPaymentData?.max_score || 0,
    score: coPaymentData?.score || 0,
  };

  const waitingPeriodsData = apiData.health_variant_ratings.find(
    (rating) => rating.label === "waitingPeriods"
  );
  const waitingPeriods = {
    id: waitingPeriodsData?.id || "",
    title: waitingPeriodsData?.title || "",
    maxScore: waitingPeriodsData?.max_score || 0,
    score: waitingPeriodsData?.score || 0,
  };

  const noClaimBonusData = apiData.health_variant_ratings.find(
    (rating) => rating.label === "noClaimBonus"
  );
  const noClaimBonus = {
    id: noClaimBonusData?.id || "",
    title: noClaimBonusData?.title || "",
    maxScore: noClaimBonusData?.max_score || 0,
    score: noClaimBonusData?.score || 0,
  };

  const transformedData: transformedHealthVariantDataTypes = {
    insurer: {
      logo: apiData.product.insurer.logo_url,
      name: apiData.product.insurer.name,
      insurer_slug: apiData.product.insurer.slug,
    },
    variant: {
      variant_name: apiData.health_variant_static_content.hero_title,
      variant_slug: apiData.variant_slug,
    },
    claimSettlementRatio: apiData.product.insurer.claim_settlement_ratio,
    networkHospitalsCount: apiData.product.insurer.network_hospital_count,
    networkHospitalsUrl: apiData.product.insurer.network_hospital_url,
    highlightedFeatures: apiData.health_variant_highlighted_features.map(
      (feature) => {
        return {
          description: feature.description,
          title: feature.title,
          icon: feature.icon_key,
        };
      }
    ),
    policyDocs: apiData.health_variant_policy_docs.map((doc) => {
      return {
        label: doc.label,
        document: doc.document_key,
      };
    }),
    verdict: apiData.health_variant_static_content.verdict,
    ratings: {
      // id: 0,
      coverage,
      claimSettlement,
      hospitalNetwork,
      coPayment,
      waitingPeriods,
      noClaimBonus,
    },
    features: apiData.feature_values.map((feature) => {
      return {
        name: feature.compare_feature.name,
        description: feature.description,
        value: feature.value,
      };
    }),
    exclusions: apiData.exclusions,

    addons: apiData.product.product_riders.map((rider) => {
      return {
        description: rider.description,
        title: rider.name,
      };
    }),
    relatedVariants: apiData.health_variant_related_variants.map((variant) => {
      return {
        id: Number(variant.related_variant_id),
        attributes: {
          name: variant.related_variant.variant_name,
          slug: variant.related_variant.variant_slug,
          logo: variant.related_variant.product.insurer
            .logo_url,
          companyName:
            variant.related_variant.product.insurer.name,
          companySlug:
            variant.related_variant.product.insurer.slug,
        },
      };
    }),

    whyOneAssure: apiData.health_variant_whyoneassures,
    faqs: apiData.health_variant_faqs.map((faq) => {
      return {
        question: faq.question,
        ans: faq.answer,
      };
    }),
    aboutThePlan: apiData.health_variant_static_content.about_the_plan,
    aboutTheCompany: apiData.product.insurer.health_insurer_static_content.legacy,
    blogs: [
      {
        title: "",
        description: "",
        image: "",
        slug: "",
      },
    ],
    seo: apiData.health_variant_seo,
  };

  return transformedData;
}
