"use client";
import Hero from "../../globals/Hero";
import { Suspense, useEffect } from "react";
import { HealthVariantData } from "../types";
import SideMenu from "@/components/globals/SideMenu";
import HighlightedFeatures from "./components/HighlightedFeatures";
import Verdict from "@/components/globals/Products/Verdict";
import About from "@/components/globals/Products/About";
import Features from "@/components/globals/Products/Features";
import Exclusions from "@/components/globals/Products/Exclusions";
import AddOns from "@/components/globals/Products/AddOns";
import Plans from "@/components/Company/components/Plans";
import NetworkHospitals from "./components/NetworkHospitals";
import WhyOneAssure from "@/components/globals/Products/WhyOneAssure";
import BookACall from "@/components/globals/Products/BookACall";
import Faqs from "@/components/globals/Products/Faq";
import { RelatedBlogs } from "@/components/globals/Products/RelatedBlogs";
import Container from "@/components/globals/Container";
import Statistics from "./components/Statistics";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { useSessionStorage } from "usehooks-ts";
import Breadcrumb from "@/components/globals/Breadcrumb";
import { transformedHealthVariantData as transformedHealthVariantDataTypes } from "./types";

export const HealthProductRoot: React.FC<{
  product: transformedHealthVariantDataTypes;
  category?: string;
}> = (props) => {
  const { product, category = "health" } = props;
  const menuItems = [
    ...(product.highlightedFeatures.length > 0
      ? [{ title: "Highlighted Feature", id: "highlightedFeature" }]
      : []),
    ...(product.verdict ? [{ title: "Our Verdict", id: "verdict" }] : []),
    ...(product.aboutThePlan
      ? [{ title: "About the Plan", id: "aboutThePlan" }]
      : []),
    ...(product.features.length > 0
      ? [{ title: "Features", id: "features" }]
      : []),
    ...(product.exclusions.length > 0
      ? [{ title: "Exclusions", id: "exclusions" }]
      : []),
    ...(product.addons.length > 0 ? [{ title: "Add-Ons", id: "addOns" }] : []),
    ...(product.relatedVariants.length > 0
      ? [{ title: "Related Variants", id: "variants" }]
      : []),
    ...(product.aboutThePlan
      ? [{ title: "About the Company", id: "aboutTheCompany" }]
      : []),
    ...(product.faqs.length > 0 ? [{ title: "FAQs", id: "faqs" }] : []),
    ...(product.blogs.length > 0
      ? [{ title: "Related Blogs", id: "relatedBlogs" }]
      : []),
  ];


  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);


  return (
    <div className="px-5 lg:px-[100px] bg-ntrl-white md:bg-soft-grey font-manrope pb-5 tracking-tightest border-t-blue-5 md:border-0 border-[.5px]">
      <Container>
        <div className="pt-3 pb-1 md:pt-5 md:pb-3">
          <Suspense>
            <Breadcrumb
              path={[
                "home",
                "health-insurance",
                product.insurer.name,
                product.variant.variant_name,
              ]}
            />
          </Suspense>
        </div>
        <div className="grid grid-cols-12 gap-5">
          <SideMenu
            menuItems={menuItems}
            classsName={menuItems.length < 3 ? "lg:hidden" : ""}
          />
          <div
            className={`col-span-12 ${
              menuItems.length < 3 ? "lg:col-span-12" : "lg:col-span-10"
            }`}
          >
            <Suspense>
              <Hero
                name={product.variant.variant_name}
                hero={{
                  title: product.variant.variant_name,
                  claimSettlementRatio: product.claimSettlementRatio,
                  networkHospitals: {
                    value: product.networkHospitalsCount.toString(),
                  }
                }}
                company={{
                  name: product.insurer.name,
                  slug: product.insurer.insurer_slug,
                  logo: {
                    data: {
                      attributes: {
                        url: product.insurer.logo,
                      }
                    }
                  },
             
                }}
                category={category}
                rating={product.ratings}
              />
            </Suspense>
            {product.highlightedFeatures.length > 0 && (
              <HighlightedFeatures
                features={product.highlightedFeatures.map((feature)=>{
                  return{
                    title: feature.title,
                    description: feature.description,
                    icon: {
                      data: {
                        attributes: {
                          url: feature.icon
                        }
                      }
                    }
                  }
                })}
                docs={product.policyDocs.map((doc)=>{
                  return{
                    label: doc.label,
                    document: {
                      data: [{ attributes: { url: doc.document } }]
                    }
                  }
                })}
              />
            )}
            {product.verdict && <Verdict oneAssureVerdict={product.verdict} />}
            {product.aboutThePlan && (
              <About
                id="aboutThePlan"
                title="About the Plan"
                about={product.aboutThePlan}
              />
            )}
            {product.ratings && <Statistics rating={product.ratings} />}
            {product.features.length > 0 && (
              <Features features={product.features.map((feature)=>{
                return{
                  title: feature.name,
                  description: feature.description,
                  listedFeatures: [{
                    feature: feature.value
                  }]
                }
              })} />
            )} 
            {product.exclusions.length > 0 && (
              <Exclusions exclusions={product.exclusions.map((exclusion)=>{
                return{
                  exclusion: exclusion
                }
              })} />
            )}
            {product.addons.length > 0 && <AddOns addOns={product.addons}/>}
            {product.relatedVariants.length > 0 && (
              <Plans
                title="Related Variants"
                category={category}
                variants={product.relatedVariants}
              />
            )}
            {product.networkHospitalsUrl &&
              product.whyOneAssure.length > 0 && (
                <div className="grid gap-x-8 grid-cols-5">
                  <NetworkHospitals
                    classname="col-span-5 lg:col-span-3"
                    url={product.networkHospitalsUrl}
                  />
                  <WhyOneAssure
                    className="col-span-5 lg:col-span-2"
                    data={product.whyOneAssure}
                  />
                </div>
              )}
            {product.aboutTheCompany && (
              <About
                id="aboutTheCompany"
                title="About the Company"
                about={product.aboutTheCompany}
              />
            )}
            <BookACall classname="md:py-10 p-5 md:px-[50px]" />
            {product.faqs.length > 0 && (
              <Faqs title="Frequently Asked Questions" faqs={product.faqs} />
            )}
            {/* {product.blogs.data.length > 0 && (
              <RelatedBlogs blogs={product.blogs} />
            )} */}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default HealthProductRoot;
