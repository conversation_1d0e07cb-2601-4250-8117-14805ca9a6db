
export  type transformedHealthVariantData = {
  
  insurer:{
    logo: string;
    name: string;
    insurer_slug:string;
  }
  
    variant:{
    variant_name:string;
    variant_slug:string;
    }
    claimSettlementRatio:number;
    networkHospitalsCount:number;
    networkHospitalsUrl:string;
    highlightedFeatures:{
        description:string;
        title:string;
        icon:string;
    }[]
    policyDocs:{
        label:string;
        document:string;
    }[]
    verdict:string;
    ratings:{
        // id: number;
        coverage: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        claimSettlement: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        hospitalNetwork: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        coPayment: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        waitingPeriods: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
        noClaimBonus: {
          id: number | string;
          title: string;
          maxScore: number;
          score: number;
        };
      }

      features:{
        name:string;
        description:string;
       value:string;
      }[]

     exclusions:string[];

     addons:{
        description:string;
        title:string;
     }[]

     relatedVariants:{
        id:number;
        attributes: {
        name:string;
        slug:string;
        logo:string;
        companyName:string;
        companySlug:string;
     }}[]

     whyOneAssure:{
        description:string,
        title:string
     }[]
     
     faqs:{
        question:string
        ans:string
     }[]

     aboutTheCompany:string
     aboutThePlan:string

     blogs:{
        title:string;
        description:string;
        image:string;
        slug:string;
     }[]

     seo:{
        id:string;
        health_variant_id?: string;
        meta_title: string;
        meta_description: string;
        meta_keyword: string;
        prevent_indexing: boolean;
        source: string;

     }
     }
