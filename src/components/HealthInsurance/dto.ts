import { HealthInsuranceLPDataObject } from "./types";

// Individual item types for the API response
type HealthLPBenefit = {
    id: string;
    title: string;
    subtitle: string;
    thumbnail_url: string;
}

type HealthLPBenefitsOfHISectionData = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type HealthLPBuyingManual = {
    id: string;
    title: string;
    description: string;
}

type HealthLPChecklist = {
    id: string;
    title: string;
    description: string;
}

type HealthLPFAQ = {
    id: string;
    question: string;
    answer: string;
}

type HealthLPHIChecklistSectionData = {
    id: string;
    title: string;
    description: string;
}

type HealthLPHIEligibilitySectionData = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type HealthLPHITopPlansType = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
    table_data: string;
}

type HealthLPHighlight = {
    id: string;
    hightlight_title: string;
    icon_url: string;
}

type HealthLPHowToBuySectionData = {
    id: string;
    title: string;
    description: string;
}

type HealthLPHowToChooseSectionData = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type HealthLPOurExpert = {
    id: string;
    title: string;
    description: string;
    logo_url: string;
}

type HealthLPOurPartner = {
    id: string;
    name: string;
    logo_url: string;
    url: string;
}

type HealthLPOverview = {
    id: string;
    title: string;
    description: string;
}

type HealthLPRequiredDocument = {
    id: string;
    title: string;
}

type HealthLPRequiredDocumentSectionData = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type HealthLPSEOCustomMeta = {
    id: string;
    mata_tag: string;
    mata_value: string;
}

type HealthLPTestimonial = {
    id: string;
    name: string;
    statement: string;
    thumbnail_url: string;
}

type HealthLPWhyBuyHIReason = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type HealthLPWhyBuyHIImportance = {
    id: string;
    title: string;
    health_lp_page_why_buy_his_hi_importances_reasons: HealthLPWhyBuyHIReason[];
}

type HealthLPWhyBuyHI = {
    id: string;
    title: string;
    health_lp_page_why_buy_his_hi_importances: HealthLPWhyBuyHIImportance[];
}

// Main API response type
export type HealthInsuranceLPAPIResponse = {
    // Basic info
    id: string;
    title: string;
    subtitle: string;
    subtitle_rich_text: string;
    
    // SEO fields
    seo_meta_title: string;
    seo_meta_description: string;
    seo_meta_keywords: string;
    seo_prevent_indexing: boolean;
    seo_source: string;
    
    // About HI section
    about_hi_title: string;
    about_hi_description: string;
    about_hi_thumbnail_url: string;
    
    // Benefits section
    benefits_of_hi_title: string;
    benefits_of_hi_description: string;
    
    // Eligibility section
    eligibility_title: string;
    eligibility_description: string;
    hi_eligibility_title: string;
    hi_eligibility_description: string;
    
    // How to buy section
    how_to_buy_title: string;
    how_to_buy_description: string;
    
    // How to choose section
    how_to_choose_title: string;
    how_to_choose_description: string;
    
    // Required documents section
    required_documents_title: string;
    required_documents_description: string;
    
    // What is HI section
    what_is_hi_title: string;
    what_is_hi_description: string;
    
    // HI checklist section
    hi_checklist_title: string;
    hi_checklist_description: string;
    
    // HI top plans section
    hi_top_plans_title: string;
    hi_top_plans_description: string;
    top_hi_plan_title: string;
    top_hi_plan_description: string;
    
    // Quote URL
    get_quote_url: string;
    
    // Arrays of data
    health_lp_page_benefits: HealthLPBenefit[];
    health_lp_page_benefits_of_hi_section_datas: HealthLPBenefitsOfHISectionData[];
    health_lp_page_buying_manuals: HealthLPBuyingManual[];
    health_lp_page_checklists: HealthLPChecklist[];
    health_lp_page_faqs: HealthLPFAQ[];
    health_lp_page_hi_checklist_section_datas: HealthLPHIChecklistSectionData[];
    health_lp_page_hi_eligibilty_section_datas: HealthLPHIEligibilitySectionData[];
    health_lp_page_hi_top_plans_types: HealthLPHITopPlansType[];
    health_lp_page_highlights: HealthLPHighlight[];
    health_lp_page_how_to_buy_section_datas: HealthLPHowToBuySectionData[];
    health_lp_page_how_to_choose_section_datas: HealthLPHowToChooseSectionData[];
    health_lp_page_our_experts: HealthLPOurExpert[];
    health_lp_page_our_partners: HealthLPOurPartner[];
    health_lp_page_overviews: HealthLPOverview[];
    health_lp_page_required_documents: HealthLPRequiredDocument[];
    health_lp_page_required_documents_section_datas: HealthLPRequiredDocumentSectionData[];
    health_lp_page_seo_custom_metas: HealthLPSEOCustomMeta[];
    health_lp_page_testimonials: HealthLPTestimonial[];
    health_lp_page_why_buy_his: HealthLPWhyBuyHI[];
}

export function transformData(apiResponse: HealthInsuranceLPAPIResponse): HealthInsuranceLPDataObject {
    // Transform highlights
    const highlights = apiResponse.health_lp_page_highlights.map((item, index) => ({
        id: index + 1,
        highlight: item.hightlight_title
    }));

    // Transform hero section
    const hero = {
        id: 1,
        title: apiResponse.title,
        subTitleRichText: apiResponse.subtitle_rich_text,
        highlights: highlights
    };

    // Transform What is HI section
    const whatIsHI = {
        id: 1,
        title: apiResponse.what_is_hi_title,
        description: apiResponse.what_is_hi_description
    };

    // Transform Benefits of HI section
    const benefitsSectionData = apiResponse.health_lp_page_benefits_of_hi_section_datas.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const benefitsOfHI = {
        id: 1,
        title: apiResponse.benefits_of_hi_title,
        description: apiResponse.benefits_of_hi_description,
        sectionData: benefitsSectionData
    };

    // Transform How to Choose section
    const howToChooseSectionData = apiResponse.health_lp_page_how_to_choose_section_datas.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const howToChoose = {
        id: 1,
        title: apiResponse.how_to_choose_title,
        description: apiResponse.how_to_choose_description,
        sectionData: howToChooseSectionData
    };

    // Transform Required Docs section
    const requiredDocsSectionData = apiResponse.health_lp_page_required_documents_section_datas.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const requiredDocs = {
        id: 1,
        title: apiResponse.required_documents_title,
        description: apiResponse.required_documents_description,
        sectionData: requiredDocsSectionData
    };

    // Transform HI Top Plans section
    const healthPlanTypes = apiResponse.health_lp_page_hi_top_plans_types.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        table: item.table_data,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const HITopPlans = {
        id: 1,
        title: apiResponse.hi_top_plans_title,
        description: apiResponse.hi_top_plans_description,
        types: healthPlanTypes
    };


    const getImportancesData = (importances: HealthLPWhyBuyHIImportance[]) => {
        return importances.map((item, index) => {
            return {
                id: index + 1,
                attributes: {
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    publishedAt: new Date().toISOString(),
                    hIImporantceReasons: item.health_lp_page_why_buy_his_hi_importances_reasons.map((reason, reasonIndex) => {
                        return {
                            id: reasonIndex + 1,
                            title: reason.title,
                            description: reason.description,
                            iconUrl: {
                                data: {
                                    id: reasonIndex + 1,
                                    attributes: {
                                        url: reason.icon_url
                                    }
                                }
                            }
                        }
                    })
                }
            }
        })
    }

    // Transform Why Buy HI section
    const gethIImportance = (whyBuyHis: HealthLPWhyBuyHI[]) => {
        return whyBuyHis.map((item, index) => {
            return {
                id: index + 1,
                title: item.title,
                hi_importances: {
                    data: getImportancesData(item.health_lp_page_why_buy_his_hi_importances)
                }
            }
        })
    }

    

    // Transform How to Buy section
    const howToBuySectionData = apiResponse.health_lp_page_how_to_buy_section_datas.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: "" // No icon in this section
                }
            }
        }
    }));

    const howToBuy = {
        id: 1,
        title: apiResponse.how_to_buy_title,
        description: apiResponse.how_to_buy_description,
        sectionData: howToBuySectionData
    };

    // Transform HI Checklist section
    const checklistSectionData = apiResponse.health_lp_page_hi_checklist_section_datas.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: "" // No icon in this section
                }
            }
        }
    }));

    const HIChecklist = {
        id: 1,
        title: apiResponse.hi_checklist_title,
        description: apiResponse.hi_checklist_description,
        sectionData: checklistSectionData
    };

    // Transform testimonials
    const testimonials = apiResponse.health_lp_page_testimonials.map((item, index) => ({
        id: index + 1,
        name: item.name,
        statement: item.statement,
        backgroundColor: "primary-3", // Default background color
        thumbnail: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.thumbnail_url
                }
            }
        }
    }));

    const testimonialsSection = {
        testimonial: testimonials
    };

    // Create the final attributes structure
    const attributes = {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        hero: hero,
        whatIsHI: whatIsHI,
        benefitsOfHI: benefitsOfHI,
        howToChoose: howToChoose,
        requiredDocs: requiredDocs,
        HITopPlans: HITopPlans,
        whyBuyHI: {
            id: 1,
            hIImporantce: gethIImportance(apiResponse.health_lp_page_why_buy_his)
        },
        howToBuy: howToBuy,
        HIChecklist: HIChecklist,
        testimonials: testimonialsSection
    };

    return {
        id: parseInt(apiResponse.id) || 1,
        attributes: attributes,
        meta: {
            metaTitle: apiResponse.seo_meta_title,
            metaDescription: apiResponse.seo_meta_description,
            keyword: apiResponse.seo_meta_keywords
        }
    };
}