import { Meta } from "@/types";
import {
  Blog,
  HighlightedFeatures,
  PolicyDocs,
  RelatedVariants,
  VariantCompany,
  VariantFeatures,
} from "../globals/types";

type DownloadLink = {
  title: string;
  url: string;
  icon_url: string;
};

type Highlights = {
  title: string;
  description: string;
  icon_url: string;
};

type SubLimit = {
  title: string;
  description: string;
};

type Feature = {
  title: string;
  details: string;
  general_description: string;
  icon_url: string;
};

type Dashboard = {
  company_icon: string;
  company_description: string;
  download_pdf_links: DownloadLink[];
  rating: number;
};

type TabsData = {
  highlights: Highlights[];
  sub_limits: SubLimit[];
  features: Feature[];
};

export type Data = {
  variant_id: string;
  company_id: string;
  product_id: string;
  slug: string;
  company_slug: string;
  dashboard: Dashboard;
  variant_name: string;
  tabs_data: TabsData;
};

// HEALTH INSURANCE LANDING PAGE DATA

type LPExpert = {
  id: number;
  title: string;
  description: string;
  icon: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

export type LPFAQ = {
  id: number;
  question: string;
  ans: string;
};

export type LPFAQs = {
  id: number;
  title: string;
  subTitle: string;
  faqs: LPFAQ[];
};

export type LPHero = {
  id: number;
  title: string;
  subTitleRichText: string;
  highlights?: {
    highlight: string;
  }[];
};

export type LPOurExperts = {
  id: number;
  experts: LPExpert[];
};

type LPWhatIsHI = {
  id: number;
  title: string;
  description: string;
};

export type LPSectionData = {
  id: number;
  title: string;
  description: string;
  icon?: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

export type LPBenefitsOfHI = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

export type LPHowToChooseHI = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

export type LPTestimonial = {
  id: number;
  name: string;
  statement: string;
  backgroundColor: string;
  thumbnail: Thumbnail;
};

export type BPTestimonials = {
  id: number;
  name: string;
  statement: string;
  backgroundColor: string;
  thumbnail: Thumbnail;
};

export type LPRequiredDocs = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

type HealthPlanType = {
  id: number;
  title: string;
  description: string;
  table: string;
  icon: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

export type HITopPlans = {
  id: number;
  title: string;
  description: string;
  types: HealthPlanType[];
};

type HIImportanceReason = {
  id: number;
  title: string;
  description: string;
  iconUrl: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

type HIImportanceData = {
  id: number;
  attributes: {
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    hIImporantceReasons: HIImportanceReason[];
  };
};

type HIImportance = {
  id: number;
  title: string;
  hi_importances: {
    data: HIImportanceData[];
  };
};

export type LPWhyBuyHI = {
  id: number;
  hIImporantce: HIImportance[];
};

export type LPHowToBuy = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

export type LPHIChecklist = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

type LPAttributes = {
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  hero: LPHero;
  whatIsHI: LPWhatIsHI;
  benefitsOfHI: LPBenefitsOfHI;
  howToChoose: LPHowToChooseHI;
  requiredDocs: LPRequiredDocs;
  HITopPlans: HITopPlans;
  whyBuyHI: LPWhyBuyHI;
  howToBuy: LPHowToBuy;
  HIChecklist: LPHIChecklist;
  testimonials: {
    testimonial: LPTestimonial[];
  };
};

export type HealthInsuranceLPDataObject = {
  id: number;
  attributes: LPAttributes;
  meta: Record<string, unknown>;
};

export interface WhyChose {
  title: string;
  description: string;
}

export interface Exclusion {
  description: string;
  title: string;
  icon: Logo;
}

export interface Hero {
  title: string;
  rating: number;
}

export interface Overview {
  overviewItem: OverviewItem[];
}

export interface OverviewItem {
  title: string;
  count: string;
  logo: { data: { attributes: { url: string } } | null };
}

export interface Documents {
  thumbail: Logo;
  title: string;
  doc: DocumentItem[];
}

export interface DocumentItem {
  title: string;
  icon: { data: { attributes: { url: string } } | null };
}

export interface Exclusions {
  title: string;
  exc: ExclusionItem[];
}

export interface ExclusionItem {
  title: string;
  description: string;
  icon: Logo;
}

export interface TFaq {
  id: number;
  question: string;
  ans: string;
}

export interface Logo {
  data: {
    attributes: {
      url: string;
    };
  };
}

export interface ProductDashboard {
  company_icon: string;
  company_description: string;
  download_pdf_links: Array<{ title: string; url: string; icon_url: string }>;
  rating: number;
}

export interface ProductHighlight {
  title: string;
  description: string;
  icon_url: string;
}

export interface ProductFeature {
  title: string;
  details: string;
  general_description: string;
  icon_url: string;
}

export interface ProductSublimit {
  title: string;
  description: string;
}

export interface ProductAttributes {
  name: string;
  slug: string;
  category: string;
  hero: Hero;
  overview?: ProductOverview;
  portability?: boolean;
  highlights?: ProductHighlights;
  features?: Features;
  eligibility?: Eligibility;
  exclusions?: {
    title: string;
    exclusion: ExclusionItem[];
  };
  whyChoose?: {
    title: string;
    item: Array<{
      title: string;
      description: string;
    }>;
  };
  faqs?: {
    id: number;
    faq: {
      id: number;
      question: string;
      ans: string;
    }[];
  };
  renewalProcess?: {
    title: string;
    step: {
      title: string;
      description: string;
    }[];
  };
  seo?: {
    metaTitle: string;
    metaDescription: string;
    keyword: string;
  };
}

export type Product = {
  id: number;
  attributes: ProductAttributes;
};

export type ProductData = {
  data: Product[];
} & Meta;

export interface ProductOverview {
  title: string;
  description: string;
  policyDocument: PolicyDocument[];
}

export type Features = {
  feat: PRFeature;
  subLimits: SubLimits;
};

export type PRFeature = {
  id: number;
  title: string;
  variant_feats: VariantFeat;
};

export type SubLimits = {
  title: string;
  limits: {
    data: {
      attributes: {
        title: string;
        key: string;
        description: string;
      };
    }[];
  };
};
export interface PolicyDocument {
  title: string;
  url: string;
}

export interface ProductHighlights {
  title: string;
  thumbnail: Logo;
  item: Array<{
    title: string;
    description: string;
  }>;
}

export type EligibilityRequirement = {
  title: string;
  logo: Logo | null;
};

export type Eligibility = {
  title: string;
  thumbnail: Logo;
  requirement: EligibilityRequirement[];
};

export type VariantFeat = {
  data: {
    id: number;
    attributes: {
      featValue: string;
      feature: {
        data: {
          id: number;
          attributes: {
            title: string;
            description: string;
          };
        };
      };
    };
  }[];
};

export type SlugsAttribute = {
  name: string;
  slug: string;
  category: string | null;
  hero: Hero;
  logo: Logo;
};

export type AllCompSlugs = {
  id: number;
  attributes: SlugsAttribute;
};

export type HealthVariantHero = {
  title: string;
  claimSettlementRatio: {
    value: number;
    // description: string;
  };
  networkHospitals: {
    value: string;
    // description: string;
    // networkHospitalsURL?: string;
  };
};

export type HealthVariantData = {
  id: string;
  variant_name: string;
  variant_slug: string;
  parent_variant_id: string | null;
  product_id?: string;
  exclusions: string[];
  product: {
    id: string;
    name: string;
    active: boolean;
    online: boolean;
    plan_type: string;
    insurer_id?: string;
    insurer: {
      claim_settlement_ratio: number;
      id: string;
      logo_url: string;
      name: string;
      network_hospital_count: number;
      network_hospital_url: string;
      slug: string;
      health_insurer_static_content: {
        legacy: string;
      };
    };
    policy_brochure_url: string;
    policy_wording_url: string;
    product_metadata: Record<string, unknown>;
    product_static_content: Record<string, unknown>;
    product_riders: Array<{
      description: string;
      id: string;
      name: string;
    }>;
  };
  feature_values: Array<{
    id: string;
    feature_id?: string;
    variant_id?: string;
    value: string;
    description: string;
    compare_feature: {
      id: string;
      name: string;
    };
  }>;
  feature_variant_scores: Array<Record<string, unknown>>;
  health_variant_faqs: Array<{
    id: string;
    question: string;
    answer: string;
  }>;
  health_variant_highlighted_features: Array<{
    id: string;
    title: string;
    description: string;
    icon_key: string;
  }>;
  health_variant_policy_docs: Array<{
    id: string;
    health_variant_id?: string;
    label: string;
    document_key: string;
  }>;
  health_variant_ratings: Array<{
    id: string;
    health_variant_id: string;
    title: string;
    label: "coverage" | "claimSettlement" | "hospitalNetwork" | "coPayment" | "waitingPeriods" | "noClaimBonus";
    score: number;
    max_score: number;
  }>;
  health_variant_related_variants: Array<{
    id: string;
    health_variant_id: string;
    related_variant_id: string;
    features: Array<Record<string, unknown>>;
    related_variant :{
      id:string;
      variant_slug:string;
      variant_name:string;
      product: {
        insurer :{
          logo_url:string;
          slug:string;
          name:string;
        }
      }
    }
  }>;
  health_variant_seo: {
    id: string;
    health_variant_id?: string;
    meta_title: string;
    meta_description: string;
    meta_keyword: string;
    prevent_indexing: boolean;
    source: string;
  };
  health_variant_static_content: {
    id: string;
    health_variant_id: string;
    hero_title: string;
    subtitle: string;
    about_the_plan: string;
    verdict: string;
  };
  health_variant_whyoneassures: Array<{
    id: string;
    title: string;
    description: string;
  }>;
};

export type HealthVariantResponse = {
  data: [
    {
      id: number;
      attributes: HealthVariantData;
    }
  ];
};

type Thumbnail = {
  data: {
    id: number;
    attributes: {
      url: string;
    };
  };
};

export type AboutTI = {
  id: number;
  title: string;
  description: string;
  thumbnail: Thumbnail;
};
