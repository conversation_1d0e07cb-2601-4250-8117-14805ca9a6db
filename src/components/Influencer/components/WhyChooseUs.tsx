"use client";
import React from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import {
  HeadingXLarge,
  HeadingSmall,
} from "@/components/UI/Typography";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { IoPeople} from "react-icons/io5";
import { htmlParser } from "@/utils/htmlParser";

type WhyChooseUsCard = {
  iconBg?: string;
  icon?: React.ReactNode;
  title: string;
  description: string;
};

const WhyChooseUsCard = ({ iconBg, icon, title, description }: WhyChooseUsCard) => (
  <div className="flex flex-col items-center gap-3 bg-white border border-primary-200 rounded-xl p-6 shadow-sm w-full h-full">
    <div
      className={`w-16 h-16 ${iconBg ? iconBg : "bg-blue-100"} rounded-full flex items-center justify-center`}
    >
      {icon ? icon : <IoPeople className="text-blue-500 text-xl" />}
    </div>
    <HeadingSmall as="h3" className="text-center text-neutral-1100">
      {title}
    </HeadingSmall>
    <div>
      {htmlParser(description, {
        classNames: {
          p: "text-center text-neutral-1100",
        },
      })}
    </div>
  </div>
);

const WhyChooseUs: React.FC<{
  whyChooseUs: {
    title: string;
    description: string;
    points?: Array<{
      title: string;
      description: string;
    }>;
  };
}> = ({ whyChooseUs }) => {


  return (
    <SectionContainerLarge className="!px-0">
      <HeadingXLarge className="text-center mb-6 text-neutral-1100">
        {whyChooseUs.title}
      </HeadingXLarge>

      <div className="border-2 border-primary-200 rounded-xl px-4 py-3 md:py-4 md:px-6 bg-white mx-6 md:mx-0 mb-4">
        {/* <div className="text-center text-neutral-1100"> */}
          {htmlParser(whyChooseUs.description, {
            classNames: {
              p: "text-neutral-1100 text-center",
            },
          })}
        {/* </div> */}
      </div>

      {/* Desktop Grid Layout */}
      {whyChooseUs.points && (
      <div className="hidden md:grid grid-cols-2 md:grid-cols-4 gap-6">
        {whyChooseUs.points.map((card, index) => (
          <WhyChooseUsCard key={index} {...card} />
        ))}
      </div>
      )}

      {/* Mobile Carousel Layout */}
      {whyChooseUs.points && (
      <div className="md:hidden">
        <MobileCarousel totalSlides={whyChooseUs.points.length}>
          {whyChooseUs.points.map((card, index) => (
            <MobileCarouselItem key={index}>
              <WhyChooseUsCard {...card} />
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
      </div>
      )}
    </SectionContainerLarge>
  );
};

export default WhyChooseUs;
