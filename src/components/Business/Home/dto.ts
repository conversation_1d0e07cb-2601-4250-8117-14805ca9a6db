import { BusinessLP } from "./type";

// Individual item types for the API response
type BusinessLPBenefit = {
    id: string;
    title: string;
    description: string;
    tag: string;
    icon_url: string;
}

type BusinessLPClient = {
    id: string;
    name: string;
    logo_url: string;
}

type BusinessLPReason = {
    id: string;
    title: string;
    description: string;
    thumbnail_url: string;
    background_color: string;
}

type BusinessLPTestimonial = {
    id: string;
    name: string;
    statement: string;
    thumbnail_url: string;
    background_color: string;
}

// Main API response type
export type BusinessLPAPIResponse = {
    id: string;
    title: string;
    subtitle: string;
    thumbnail_url: string;
    business_lp_benefits: BusinessLPBenefit[];
    business_lp_clients: BusinessLPClient[];
    business_lp_reasons: BusinessLPReason[];
    business_lp_testimonials: BusinessLPTestimonial[];
}

export function transformData(apiResponse: BusinessLPAPIResponse): BusinessLP {
    const ourClients = {
        id: 1,
        client: apiResponse.business_lp_clients.map((client, index) => ({
            id: index + 1,
            name: client.name,
            logo: {
                data: {
                    id: index + 1,
                    attributes: {
                        url: client.logo_url
                    }
                }
            }
        }))
    }

    const comprehensiveBenefit = {
        id: 1,
        benefits: apiResponse.business_lp_benefits.map((benefit, index) => ({
            id: index + 1,
            tag: benefit.tag,
            title: benefit.title,
            description: benefit.description,
            icon: {
                data: {
                    id: 1,
                    attributes: {
                        url: benefit.icon_url
                    }
                }
            }
        }))
    }
    const whyUs = {
        id: 1,
        reason: apiResponse.business_lp_reasons.map((reason, index) => ({
            id: index + 1,
            title: reason.title,
            desc: reason.description,
            thumbnail: {
                data: {
                    id: 1,
                    attributes: {
                        url: reason.thumbnail_url
                    }
                }
            },
            backgroundColor: reason.background_color
        }))
    }

    const testimonials = {
        id: 1,
        testimonial: apiResponse.business_lp_testimonials.map((testimonial, index) => ({
            id: index + 1,
            name: testimonial.name,
            statement: testimonial.statement,
            backgroundColor: testimonial.background_color,
            thumbnail: {
                data: {
                    id: index + 1,
                    attributes: {
                        url: testimonial.thumbnail_url
                    }
                }
            }
        }))
    }

    return {
        id: 1,
        attributes: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            publishedAt: new Date().toISOString(),
            hero: {
                id: 1,
                title: apiResponse.title,
                subtitle: apiResponse.subtitle,
            },
            ourClients: ourClients,
            comprehensiveBenefit: comprehensiveBenefit,
            whyUs: whyUs,
            testimonials: testimonials,
        }
    };
}