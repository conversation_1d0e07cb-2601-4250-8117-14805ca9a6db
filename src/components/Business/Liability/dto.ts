import { LiabilityLPResponse } from "./type";

// Individual item types for the API response
type LiabilityLPWhyUsReason = {
    id: string;
    title: string;
    description: string;
    thumbnail_url: string;
    background_color: string;
}

type LiabilityLPHighlight = {
    id: string;
    hightlight_title: string;
    icon_url: string;
}

type LiabilityLPCoverItem = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

// Main API response type
export type LiabilityLPAPIResponse = {
    id: string;
    title: string;
    thumbnail_url: string;
    liability_lp_page_whyus_reasons: LiabilityLPWhyUsReason[];
    liability_lp_page_highlights: LiabilityLPHighlight[];
    liability_lp_page_cover_items: LiabilityLPCoverItem[];
}

export function transformData(apiResponse: LiabilityLPAPIResponse): LiabilityLPResponse {
    // Transform highlights
    const highlights = apiResponse.liability_lp_page_highlights.map((item, index) => ({
        id: index + 1,
        highlight: item.hightlight_title,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    // Transform hero section
    const hero = {
        id: 1,
        title: apiResponse.title,
        highlights: highlights
    };

    // Transform why us reasons
    const reasons = apiResponse.liability_lp_page_whyus_reasons.map((item, index) => ({
        id: index + 1,
        title: item.title,
        desc: item.description,
        backgroundColor: item.background_color,
        thumbnail: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.thumbnail_url
                }
            }
        }
    }));

    const whyUs = {
        id: 1,
        reason: reasons
    };

    // Transform cover items
    const coverItems = apiResponse.liability_lp_page_cover_items.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const cover = {
        id: 1,
        coverItem: coverItems
    };

    // Create the final attributes structure
    const attributes = {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        hero: hero,
        whyUs: whyUs,
        cover: cover
    };

    return {
        id: 1,
        attributes: attributes
    };
}