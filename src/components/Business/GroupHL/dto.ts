import { GroupLPResponse } from "./type";

type GroupLPBenefitItem = {
    id: string;
    tag: string;
    title: string;
    icon_url: string;
    description: string;
}

type GroupLPFAQ = {
    id: string;
    question: string;
    answer: string;
}

type GroupLPFeature = {
    id: string;
    tag: string;
    title: string;
    icon_url: string;
    description: string;
}

type GroupLPHighlight = {
    id: string;
    icon_url: string;
    hightlight_title: string;
}

type GroupLPPartner = {
    id: string;
    name: string;
    logo_url: string;
    url: string;
}

type GroupLPReason = {
    id: string;
    thumbnail_url: string;
    title: string;
    description: string;
    background_color: string;
}

type GroupLPTestimonial = {
    id: string;
    name: string;
    statement: string;
    thumbnail_url: string;
    background_color: string;
}

export type GroupLPAPIResponse = {
    id: string;
    title: string;
    group_lp_page_benefit_items: GroupLPBenefitItem[];
    group_lp_page_faqs: GroupLPFAQ[];
    group_lp_page_features: GroupLPFeature[];
    group_lp_page_highlights: GroupLPHighlight[];
    group_lp_page_our_partners: GroupLPPartner[];
    group_lp_page_reasons: GroupLPReason[];
    group_lp_page_testimonials: GroupLPTestimonial[];
}

const transformData = (apiResponse: GroupLPAPIResponse): GroupLPResponse => {
    // Transform highlights
    
    const highlights = apiResponse.group_lp_page_highlights.map((item, index) => ({
        id: index + 1,
        highlight: item.hightlight_title,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    // Transform hero section
    const hero = {
        id: 1,
        title: apiResponse.title,
        highlight: highlights
    };

    // Transform features for digital dashboard
    const features = apiResponse.group_lp_page_features.map((item, index) => ({
        id: index + 1,
        tag: item.tag,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const digitalDashboard = {
        id: 1,
        features: features
    };

    // Transform testimonials
    const testimonials = apiResponse.group_lp_page_testimonials.map((item, index) => ({
        id: index + 1,
        name: item.name,
        statement: item.statement,
        backgroundColor: item.background_color,
        thumbnail: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.thumbnail_url
                }
            }
        }
    }));

    const testimonialsSection = {
        id: 1,
        testimonial: testimonials
    };

    // Transform FAQs
    const faqs = apiResponse.group_lp_page_faqs.map((item, index) => ({
        id: index + 1,
        question: item.question,
        ans: item.answer
    }));

    const faqsSection = {
        id: 1,
        faq: faqs
    };

    // Transform benefit items
    const benefitItems = apiResponse.group_lp_page_benefit_items.map((item, index) => ({
        id: index + 1,
        tag: item.tag,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const groupBenefit = {
        id: 1,
        benefitItem: benefitItems
    };

    // Transform reasons (why us)
    const reasons = apiResponse.group_lp_page_reasons.map((item, index) => ({
        id: index + 1,
        title: item.title,
        desc: item.description,
        backgroundColor: item.background_color,
        thumbnail: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.thumbnail_url
                }
            }
        }
    }));

    const whyUs = {
        id: 1,
        reason: reasons
    };

    // Create the final response structure
    const attributes = {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        hero: hero,
        digitalDashboard: digitalDashboard,
        testimonials: testimonialsSection,
        faqs: faqsSection,
        grpBenefit: groupBenefit,
        whyUs: whyUs
    };

    return {
        id: parseInt(apiResponse.id) || 1,
        attributes: attributes
    };
}

export default transformData;