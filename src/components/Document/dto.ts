// Individual item types for the API response
type OADocFile = {
    id: string;
    label: string;
    file_key: string;
}

// Main API response type
export type DocumentAPIResponse = {
    id: string;
    title: string;
    slug: string;
    content: string;
    show_doc: boolean;
    oa_doc_files: OADocFile[];
}

export function transformData(apiResponse: DocumentAPIResponse): any {
    
    return {
        id: 1,
        title: apiResponse.title,
        slug: apiResponse.slug,
        content: apiResponse.content,
        showDoc: apiResponse.show_doc,
        docFile: apiResponse.oa_doc_files.map((file, index) => ({
            title: file.label,
            file:{
                data:{
                    attributes:{
                        url: file.file_key
                    }
                }
            }
        }))
    };
}