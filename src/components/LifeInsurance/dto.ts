import { TermInsuranceLPDataObject } from "./types";

// Individual item types for the GraphQL response
type TermLPBenefit = {
    id: string;
    title: string;
    subtitle: string;
    icon_url: string;
}

type TermLPBenefitsOfTI = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type TermLPBuyFromOAS = {
    id: string;
    title: string;
    description: string;
}

type TermLPDocument = {
    id: string;
    title: string;
}

type TermLPFAQ = {
    id: string;
    question: string;
    answer: string;
}

type TermLPHighlight = {
    id: string;
    hightlight_title: string;
    icon_url: string;
}

type TermLPHowToBuyTI = {
    id: string;
    title: string;
    description: string;
}

type TermLPHowToBuyTIOneAssure = {
    id: string;
    title: string;
    description: string;
}

type TermLPImportance = {
    id: string;
    title: string;
    subtitle: string;
    thumbnail_url: string;
}

type TermLPPartner = {
    id: string;
    name: string;
    logo_url: string;
    url: string;
}

type TermLPRequiredDocTI = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type TermLPRider = {
    id: string;
    title: string;
    description: string;
}

type TermLPRiderForTI = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type TermLPSEOCustomMeta = {
    id: string;
    meta_tag: string;
    meta_value: string;
}

type TermLPTestimonial = {
    id: string;
    name: string;
    statement: string;
    thumbnail_url: string;
    background_color: string;
}

type TermLPTIEligibility = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type TermLPWhoShouldBuyTI = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type TermLPWhoToBuyTI = {
    id: string;
    title: string;
    description: string;
    icon_url: string;
}

type TermLPWhyBuyTI = {
    id: string;
    title: string;
    description: string;
    thumbnail_url: string;
    background_color: string;
}

type TermLPWhatWeOfferIcon = {
    id: string;
    icon_url: string;
}

type TermLPWhatWeOffer = {
    id: string;
    title: string;
    subtitle: string;
    term_lp_what_we_offer_icons: TermLPWhatWeOfferIcon[];
}

// Main GraphQL response type
export type TermInsuranceAPIResponse = {
    id: string;
    title: string;
    subtitle: string;
    subtitle_rich_text: string;
    
    // SEO fields
    seo_meta_title: string;
    seo_meta_description: string;
    seo_meta_keywords: string;
    seo_prevent_indexing: boolean;
    seo_source: string;
    
    // About TI section
    about_ti_title: string;
    about_ti_description: string;
    about_ti_thumbnail_url: string;
    
    // Benefits section
    benefits_of_ti_title: string;
    benefits_of_ti_description: string;
    
    // Eligibility section
    ti_eligibility_title: string;
    ti_eligibility_description: string;
    eligibility_description: string;
    
    // How it works section
    how_does_ti_works_title: string;
    how_does_ti_works_description: string;
    how_ti_works_description: string;
    
    // How to buy section
    how_to_buy_ti_title: string;
    how_to_buy_ti_description: string;
    how_to_buy_ti_oneassure: string;
    how_to_buy_ti_oneassure_description: string;
    
    // Required documents section
    required_documents_title: string;
    required_documents_description: string;
    
    // Riders section
    ride_for_ti_title: string;
    ride_for_ti_description: string;
    
    // Who should buy section
    who_should_buy_ti_title: string;
    who_should_buy_ti_description: string;
    who_buys_ti_description: string;
    
    // Why buy from OneAssure section
    who_to_buy_ti_title: string;
    who_to_buy_ti_description: string;
    
    // What is TI section
    what_is_ti_title: string;
    what_is_ti_description: string;
    
    // Quote URL
    get_quote_url: string;
    
    // Arrays of data
    term_lp_page_benefits: TermLPBenefit[];
    term_lp_page_benefits_of_tis: TermLPBenefitsOfTI[];
    term_lp_page_buy_from_oas: TermLPBuyFromOAS[];
    term_lp_page_documents: TermLPDocument[];
    term_lp_page_faqs: TermLPFAQ[];
    term_lp_page_highlights: TermLPHighlight[];
    term_lp_page_how_to_buy_tis: TermLPHowToBuyTI[];
    term_lp_page_how_to_buys: TermLPHowToBuyTI[];
    term_lp_page_how_to_ti_onesassures: TermLPHowToBuyTIOneAssure[];
    term_lp_page_importances: TermLPImportance[];
    term_lp_page_our_partners: TermLPPartner[];
    term_lp_page_required_docs_tis: TermLPRequiredDocTI[];
    term_lp_page_riders: TermLPRider[];
    term_lp_page_rides_for_tis: TermLPRiderForTI[];
    term_lp_page_seo_custom_metas: TermLPSEOCustomMeta[];
    term_lp_page_testimonials: TermLPTestimonial[];
    term_lp_page_ti_eligibilitys: TermLPTIEligibility[];
    term_lp_page_who_should_buy_tis: TermLPWhoShouldBuyTI[];
    term_lp_page_who_to_buy_tis: TermLPWhoToBuyTI[];
    term_lp_page_why_buy_tis: TermLPWhyBuyTI[];
    term_lp_what_we_offers: TermLPWhatWeOffer[];
}

export function transformData(apiResponse: TermInsuranceAPIResponse): TermInsuranceLPDataObject {
    
    // Transform highlights
    const highlights = apiResponse.term_lp_page_highlights.map((item, index) => ({
        id: index + 1,
        highlight: item.hightlight_title
    }));

    // Transform hero section
    const hero = {
        id: 1,
        title: apiResponse.title,
        subTitleRichText: apiResponse.subtitle_rich_text,
        highlights: highlights
    };

    // Transform testimonials
    const testimonials = apiResponse.term_lp_page_testimonials.map((item, index) => ({
        id: index + 1,
        name: item.name,
        statement: item.statement,
        backgroundColor: item.background_color,
        thumbnail: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.thumbnail_url
                }
            }
        }
    }));

    const testimonialsSection = {
        id: 1,
        testimonial: testimonials
    };

    // Transform What is TI section
    const whatIsTI = {
        id: 1,
        title: apiResponse.what_is_ti_title,
        description: apiResponse.what_is_ti_description
    };

    // Transform How does TI work section
    const howDoesTIWorks = {
        id: 1,
        title: apiResponse.how_does_ti_works_title,
        description: apiResponse.how_does_ti_works_description
    };

    // Transform Benefits of TI section
    const benefitsSectionData = apiResponse.term_lp_page_benefits_of_tis.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const benefitsOfTI = {
        id: 1,
        title: apiResponse.benefits_of_ti_title,
        description: apiResponse.benefits_of_ti_description,
        sectionData: benefitsSectionData
    };

    // Transform Who should buy TI section
    const whoShouldBuySectionData = apiResponse.term_lp_page_who_should_buy_tis.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const whoShouldBuyTI = {
        id: 1,
            title: apiResponse.who_should_buy_ti_title,
        description: apiResponse.who_should_buy_ti_description,
        sectionData: whoShouldBuySectionData
    };

    // Transform Why to buy TI section
    const whyToBuySectionData = apiResponse.term_lp_page_who_to_buy_tis.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));


    const whyToBuyTI = {
        id: 1,
        title: apiResponse.who_to_buy_ti_title,
        description: apiResponse.who_to_buy_ti_description,
        sectionData: whyToBuySectionData
    };

    // Transform How to buy TI section
    const howToBuySectionData = apiResponse.term_lp_page_how_to_buy_tis.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: "" // No icon in this section
                }
            }
        }
    }));

    const howToBuyTI = {
        id: 1,
        title: apiResponse.how_to_buy_ti_title,
        description: apiResponse.how_to_buy_ti_description,
        sectionData: howToBuySectionData
    };

    // Transform Rider for TI section
    const riderSectionData = apiResponse.term_lp_page_rides_for_tis.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const riderForTI = {
        id: 1,
        title: apiResponse.ride_for_ti_title,
        description: apiResponse.ride_for_ti_description,
        sectionData: riderSectionData
    };

    // Transform How to buy TI OneAssure section
    const howToBuyOneAssureSectionData = apiResponse.term_lp_page_how_to_ti_onesassures.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: "" // No icon in this section
                }
            }
        }
    }));

    const howToBuyTIOneAssure = {
        id: 1,
        title: apiResponse.how_to_buy_ti_oneassure,
        description: apiResponse.how_to_buy_ti_oneassure_description,
        sectionData: howToBuyOneAssureSectionData
    };

    // Transform TI Eligibility section
    const eligibilitySectionData = apiResponse.term_lp_page_ti_eligibilitys.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const TIEligibility = {
        id: 1,
        title: apiResponse.ti_eligibility_title,
        description: apiResponse.ti_eligibility_description,
        sectionData: eligibilitySectionData
    };

    // Transform Required Docs TI section
    const requiredDocsSectionData = apiResponse.term_lp_page_required_docs_tis.map((item, index) => ({
        id: index + 1,
        title: item.title,
        description: item.description,
        icon: {
            data: {
                id: index + 1,
                attributes: {
                    url: item.icon_url
                }
            }
        }
    }));

    const requiredDocsTI = {
        id: 1,
        title: apiResponse.required_documents_title,
        description: apiResponse.required_documents_description,
        sectionData: requiredDocsSectionData
    };

    // Create the final attributes structure
    const attributes = {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString(),
        hero: hero,
        testimonials: testimonialsSection,
        whatIsTI: whatIsTI,
        HowDoesTIWorks: howDoesTIWorks,
        benefitsOfTI: benefitsOfTI,
        whoShouldBuyTI: whoShouldBuyTI,
        whyToBuyTI: whyToBuyTI,
        howToBuyTI: howToBuyTI,
        riderForTI: riderForTI,
        howToBuyTIOneAssure: howToBuyTIOneAssure,
        TIEligibility: TIEligibility,
        requiredDocsTI: requiredDocsTI
    };

    return {
        id: 1,
        attributes: attributes,
    };
}