import { TermProductApiResponse } from "./types";
import { TermVariantData } from "../types";
import { RelatedVariants } from "@/components/globals/types";

export const transformTermProductData = (
  data: TermProductApiResponse
): TermVariantData => {
  const hero = {
    title: data.term_variant_static_content.hero_title,
    claimSettlementRatio:
      data.term_variant_static_content.claim_settlement_ratio,
  };

  const company = {
    name: data.product.insurer.name,
    slug: data.product.insurer.temp_slug,
    logo: {
      data: {
        attributes: {
          url: data.product.insurer.logo_url,
        },
      },
    },
    legecy: data.product.insurer.term_insurer_static_content.legacy,
  };

  const relatedVariants: RelatedVariants =
    data.term_variant_related_variants.map((variant) => ({
      id: variant.id,
      relatedVariant: {
        data: {
          id: variant.related_variant.id,
          attributes: {
            name: variant.related_variant.variant_name,
            slug: variant.related_variant.variant_slug,
            company: {
              data: {
                attributes: {
                  name: variant.related_variant.product.insurer.name,
                  slug: variant.related_variant.product.insurer.slug,
                  logo: {
                    data: {
                      attributes: {
                        url: variant.related_variant.product.insurer.logo_url,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      features: variant.features,
    }));

  return {
    name: data.variant_name,
    slug: data.variant_slug,
    verdict: data.term_variant_static_content.verdict,
    aboutThePlan: data.term_variant_static_content.about_the_plan,
    company: {
      data: {
        attributes: company,
      },
    },
    faqs: data.term_variant_faqs.map((faq) => ({
      question: faq.question,
      ans: faq.answer,
    })),
    whyOneAssure: data.term_variant_why_one_assures.map((whyOneAssure) => ({
      title: whyOneAssure.title,
      description: whyOneAssure.description,
    })),
    features: data.term_variant_features.map((feature) => ({
      title: feature.title,
      listedFeatures: feature.listed_features.map((feature) => ({
        feature: feature,
      })),
      description: feature.description,
    })),
    exclusions: data.term_variant_static_content.exclusions.map(
      (exclusion) => ({
        exclusion: exclusion,
      })
    ),
    addOns: data.term_variant_addons.map((addon) => ({
      title: addon.title,
      description: addon.description,
    })),
    eligibility: data.term_variant_eligibility.map((eligibility) => ({
      title: eligibility.title,
      description: eligibility.description,
    })),
    hero: hero,
    policyDocs: data.term_variant_policy_docs.map((policyDoc) => ({
      label: policyDoc.label,
      document: {
        data: [
          {
            attributes: {
              url: policyDoc.document_key,
            },
          },
        ],
      },
    })),
    variants: relatedVariants,
    womenBenifits: data.term_variant_static_content.women_benefits.map(
      (womenBenifit) => ({
        description: womenBenifit,
      })
    ),
    smokingDownsides: data.term_variant_static_content.smoking_downsides.map(
      (smokingDownside) => ({
        description: smokingDownside,
      })
    ),
    notes: data.term_variant_notes.map((note) => ({
      title: note.title,
      description: note.description,
    })),
    seo: {
      metaTitle: data.term_variant_seo.meta_title,
      metaDescription: data.term_variant_seo.meta_description,
      keyword: data.term_variant_seo.keywords,
    },
    blogs: {
      data: [
      ],
    },
  };
};
