export type TermProductApiResponse = {
    id: string;
    variant_name: string;
    variant_slug: string;
    temp_slug: string;
    product: {
      insurer: {
        name: string;
        logo_url: string;
        id: string;
        temp_slug: string;
        term_insurer_ratings: {
          id: string;
          solvency: number;
          icr: number;
          growth: number;
          aum: number;
          one_assure_rating: number;
        };
        term_insurer_static_content: {
          legacy: string;
        }
      };
    };
    term_variant_addons: {
      id: string;
      title: string;
      description: string;
    }[];
    term_variant_eligibility: {
      id: string;
      title: string;
      description: string;
    }[];
    term_variant_faqs: {
      id: string;
      question: string;
      answer: string;
    }[];
    term_variant_features: {
      id: string;
      title: string;
      listed_features: string[];
      description: string;
    }[];
    term_variant_notes: {
      id: string;
      title: string;
      description: string;
    }[];
    term_variant_policy_docs: {
      id: string;
      label: string;
      document_key: string;
    }[];
    term_variant_related_variants: {
      id: string;
      term_variant_id: string;
      related_variant_id: string;
      features: string[];
      related_variant: {
        id: string;
        variant_name: string;
        variant_slug: string;
        product: {
          insurer: {
            name: string;
            slug: string;
            logo_url: string;
          }
        }
      }
    }[];
    term_variant_seo: {
      id: string;
      meta_title: string;
      meta_description: string;
      keywords: string;
      prevent_indexing: boolean;
      source: string;
    };
    term_variant_static_content: {
      id: string;
      hero_title: string;
      exclusions: string[];
      claim_settlement_ratio: number;
      about_the_plan: string;
      smoking_downsides: string[];
      verdict: string;
      women_benefits: string[];
    };
    term_variant_why_one_assures: {
      id: string;
      title: string;
      description: string;
    }[];
}