"use client";

import Image from "next/image";
import { usePathname } from "next/navigation";
import Link from "next/link";
import BookACallBtn from "@/components/globals/BookACall";
import RiskGaugeMeter from "./RiskGaugeMeter";
import CircularProgress from "@/components/globals/Products/CircularProgress";

const Hero: React.FC<{
  title: string;
  rating: number;
  logo?: string;
  category?: string;
  claimSettlementRatio?: number;
  networkHospitals?: number | string;
}> = ({
  title,
  rating,
  logo,
  category = "health",
  claimSettlementRatio,
  networkHospitals,
}) => {
  const path = usePathname();
  const pathArr = path.substring(1).split("/");
  let link = "";

  const handleClick = (id: string) => {
    // Get the element
    const element = document.getElementById(id);
    if (element) {
      // Smooth scroll to element
      element.scrollIntoView({ behavior: "smooth", block: "center" });
      // Update URL hash
      const newUrl = `${window.location.pathname}#${id}`;
      window.history.replaceState(null, "", newUrl);
    }
  };

  return (
    <div
      className={`rounded-3xl pt-5 md:py-7 pb-6 md:px-12 bg-white my-4 md:my-0 px-5 py-4 ${
        category === "health" ? "md:pb-12" : ""
      }`}
    >
      <div
        className={`flex ${
          category === "health" ? "gap-7" : "gap-10 md:mb-2"
        } justify-between`}
      >
        <div className={`flex flex-col w-full`}>
          <div className=" flex items-start md:items-center gap-2.5 md:gap-4">
            {/* Logo */}
            <div className="w-24 h-24 mb-2 relative">
              <Image
                src={logo ? logo : ""}
                fill={true}
                style={{ objectFit: "contain" }}
                alt={title}
              />
            </div>

            {/* Name of the company & portability status */}
            <div className="">
              <h1 className="text-[28px]/[30px] md:text-[40px]/[48px] font-medium mb-2 md:mb-3">
                {title}
              </h1>
            </div>
          </div>

          {category === "health" ? (
            <div className="flex md:block gap-3 mt-3 md:mt-auto">
              <div className="flex md:hidden border-[0.5px] border-blue-5 flex-col items-center justify-end text-[36px]/[40px] p-2 rounded-3xl w-[45%]">
                <CircularProgress
                  percentage={(rating / 10) * 100}
                  text={rating.toString()}
                />
                <p className="text-center w-full text-[12px]/[16px] md:text-[16px]/5 px-2 py-1 mx-auto rounded-3xl border-[0.5px] border-blue-5 bg-white">
                  OneAssure Rating
                </p>
              </div>
              <div className="w-[55%] md:w-full flex flex-col-reverse md:flex-row items-center justify-between gap-3 md:gap-7 mb-1.5 md:mb-2.5">
                {claimSettlementRatio && (
                  <div className="relative flex justify-center text-[26px]/[36px] md:text-[36px]/[40px] w-full md:w-1/2 pt-4 md:pt-[10px] pb-4 rounded-3xl border-[0.5px] border-blue-5 font-poppins">
                    {claimSettlementRatio}%
                    <div className="absolute text-[12px]/[16px] md:text-[16px]/5 px-[10px] rounded-3xl border-[0.5px] border-blue-5 bg-white top-[3.6rem] md:top-14 font-manrope">
                      Claim Settlement Ratio
                    </div>
                  </div>
                )}
                {networkHospitals && (
                  <div className="relative flex justify-center text-[26px]/[36px] md:text-[36px]/[40px] w-full md:w-1/2 pt-4 md:pt-[10px] pb-4 rounded-3xl border-[0.5px] border-blue-5 font-poppins">
                    {networkHospitals}+
                    <div className="absolute text-[12px]/[16px] md:text-[16px]/5 px-[10px] rounded-3xl border-[0.5px] border-blue-5 bg-white top-[3.6rem] md:top-14 font-manrope">
                      Network Hospitals
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex md:block gap-3 mt-3 md:mt-auto">
              <div className="flex md:hidden border-[0.5px] border-blue-5  flex-col items-center justify-end text-[36px]/[40px] p-2 rounded-3xl w-full">
                <CircularProgress
                  percentage={(rating / 10) * 100}
                  text={rating.toString()}
                />
                <p className="text-center w-full text-[12px]/[16px] md:text-[16px]/5 px-2 py-1 mx-auto rounded-3xl border-[0.5px] border-blue-5 bg-white">
                  OneAssure Rating
                </p>
              </div>
              <div className="md:mt-9 gap-3 relative flex md:flex-row flex-col items-center md:justify-center justify-end text-[36px]/[40px] md:text-[36px]/[40px] w-full py-[10px] rounded-3xl border-[0.5px] border-blue-5 font-poppins">
                <div className="hidden md:block text-[24px]/[32px] pr-14 font-manrope ">
                  Claim Settlement Ratio
                </div>
                {claimSettlementRatio}%
                <div className="md:hidden text-center py-2.5 mx-2.5 block text-[12px]/[16px] md:text-[16px]/5 px-[10px] rounded-3xl bg-white font-manrope">
                  Claim Settlement Ratio
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="hidden md:flex border-[0.5px] border-blue-5 flex-col items-center justify-end text-[36px]/[40px]  pb-3 px-3 rounded-3xl w-1/4">
          <CircularProgress
            percentage={(rating / 10) * 100}
            text={rating.toString()}
          />
          <p className="w-full text-center text-[12px]/[16px] md:text-[16px]/5 px-2 py-1 rounded-3xl border-[0.5px] border-blue-5 bg-white">
            OneAssure Rating
          </p>
        </div>
      </div>
    </div>
  );
};

export default Hero;
