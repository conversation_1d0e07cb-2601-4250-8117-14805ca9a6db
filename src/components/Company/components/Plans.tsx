import Image from "next/image";
import Link from "next/link";

import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";

import Container from "@/components/globals/Container";
import { Variant } from "@/components/globals/types";

const Plans: React.FC<{
  variants: Array<Variant>;
  title: string;
  category: string;
}> = (props) => {
  return (
    <section
      className="mt-5 px-3 md:px-[50px] py-4 md:py-7 scroll-m-28 bg-white rounded-3xl"
      id="variants"
    >
      <div>
        <div className="text-[20px]/[24px] md:text-[32px]/[36px] text-ntrl-black gap-2 flex items-start md:px-2.5 py-[10px]">
          <h2 className="font-medium">{props.title}</h2>
        </div>

        {/* Plan cards here */}
        <div className="grid grid-cols-1 md:grid-cols-2 md:gap-5 gap-[10px]">
          {props.variants.map((variant: Variant, idx: number) => variant.attributes.slug &&(
            <Link
              href={
                props.category === "term"
                  ? `/term-insurance/${variant.attributes.companySlug}/${variant.attributes.slug}`
                  : `/health-insurance/${variant.attributes.companySlug}/${variant.attributes.slug}`
              }
              key={idx}
              className="bg-[white] border-blue-5 rounded-xl p-2 md:p-5 flex flex-row items-center md:items-center cursor-pointer border-[0.5px] "
            >
              {/* logo */}
              <div className="w-[25%] md:w-[20%] h-[30px] relative">
                <Image
                  src={variant.attributes.logo}
                  fill={true}
                  style={{ objectFit: "contain" }}
                  alt={variant.attributes.companyName}
                />
              </div>
              {/* details */}
              <div className="w-full px-4 md:px-5 ">
                <h2 className="text-left text-[14px]/[20px] md:text-[16px]/[26px] text-ntrl-black font-normal">
                  {variant.attributes.name}
                </h2>
              </div>
              <ArrowTopRightOnSquareIcon className="md:w-[35px] w-8 md:h-[35px] h-8 font-semibold text-primary-1" />
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Plans;
