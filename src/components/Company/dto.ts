import {
  Company,
  Plans,
  TermInsurerApiResponse,
  TermInsurerSlugData,
} from "@/components/Company/types";

export const transformCompanyData = (data: TermInsurerApiResponse): Company => {
  const term_variants = data.products
    .map((product) => {
      return product.product_variants.map((variant) => ({
        id: variant.id,
        attributes: {
          name: variant.variant_name,
          slug: variant.temp_slug,
        },
      }));
    })
    .flat();

  const claimSettlement = {
    title: "Claim Settlement",
    cashless: {
      data: {
        attributes: {
          steps: data.term_insurer_claim_settlements
            .flatMap((settlement) =>
              settlement.term_insurer_claim_settlement_types
                .filter((type) => type.type === "cashless claim")
                .map((type) =>
                  type.term_insurer_claim_settlement_steps.map((step) => ({
                    title: step.title,
                    description: step.description,
                  }))
                )
            )
            .flat(),
        },
      },
    },
    reimbursement: {
      data: {
        attributes: {
          steps: data.term_insurer_claim_settlements
            .flatMap((settlement) =>
              settlement.term_insurer_claim_settlement_types
                .filter((type) => type.type === "reimbursement claim")
                .map((type) =>
                  type.term_insurer_claim_settlement_steps.map((step) => ({
                    title: step.title,
                    description: step.description,
                  }))
                )
            )
            .flat(),
        },
      },
    },
  };

  const renewalSteps = {
    title: "Renewal Steps",
    onlineRenewal: {
      data: {
        attributes: {
          steps: data.term_insurer_renewal_steps
            .flatMap((step) =>
              step.term_insurer_renewal_types
                .filter((type) => type.type === "online renewal")
                .map((type) =>
                  type.term_insurer_renewal_type_steps.map((step) => ({
                    title: step.title,
                    description: step.description,
                  }))
                )
            )
            .flat(),
        },
      },
    },
    offlineRenewal: {
      data: {
        attributes: {
          steps: data.term_insurer_renewal_steps
            .flatMap((step) =>
              step.term_insurer_renewal_types
                .filter((type) => type.type === "offline renewal")
                .map((type) =>
                  type.term_insurer_renewal_type_steps.map((step) => ({
                    title: step.title,
                    description: step.description,
                  }))
                )
            )
            .flat(),
        },
      },
    },
  };

  const grossDirectPremium = data.term_insurer_statistics.find(
    (stat) => stat.type === "gross direct premium"
  );
  const icr = data.term_insurer_statistics.find((stat) => stat.type === "icr");
  const premiumUnderwritten = data.term_insurer_statistics.find(
    (stat) => stat.type === "premium underwritten"
  );
  const solvencyRatio = data.term_insurer_statistics.find(
    (stat) => stat.type === "solvency ratio"
  );
  const statistics = {
    grossDirectPremium: {
      industry: grossDirectPremium?.industry || 0,
      company: grossDirectPremium?.company || 0,
      description: grossDirectPremium?.description || "",
    },
    icr: {
      industry: icr?.industry || 0,
      company: icr?.company || 0,
      description: icr?.description || "",
    },
    premiumUnderwritten: {
      industry: premiumUnderwritten?.industry || 0,
      company: premiumUnderwritten?.company || 0,
      description: premiumUnderwritten?.description || "",
    },
    solvencyRatio: {
      industry: solvencyRatio?.industry || 0,
      company: solvencyRatio?.company || 0,
      description: solvencyRatio?.description || "",
    },
  };

  return {
    id: data.id,
    attributes: {
      name: data.name,
      slug: data.temp_slug,
      hero: {
        title: data.term_insurer_static_content.hero_title,
        rating: 0,
      },
      logo: {
        data: {
          attributes: {
            url: data.logo_url,
          },
        },
      },
      verdict: data.term_insurer_static_content.verdict,
      pros: data.term_insurer_pros_cons
        .filter((prosCons) => prosCons.type === "pro")
        .map((prosCons) => ({
          title: prosCons.title,
          description: prosCons.description,
        })),
      cons: data.term_insurer_pros_cons
        .filter((prosCons) => prosCons.type === "con")
        .map((prosCons) => ({
          title: prosCons.title,
          description: prosCons.description,
        })),
      kycDocs: data.term_insurer_static_content.kyc_docs.map((kycDoc) => ({
        name: kycDoc,
      })),
      legecy: data.term_insurer_static_content.legacy,
      faqs: data.term_insurer_faqs.map((faq) => ({
        question: faq.question,
        ans: faq.answer,
      })),
      renewalKeyPoints: data.term_insurer_static_content.renewal_key_points.map(
        (renewalKeyPoint) => ({
          point: renewalKeyPoint,
        })
      ),
      claimSettlement: claimSettlement,
      renewalSteps: renewalSteps,
      customerSupport: {
        email: data.term_insurer_static_content.customer_support_email,
        contactNumber: data.term_insurer_static_content.customer_support_number,
      },
      term_variants: {
        data: term_variants,
      },
      policyGuide: {
        title: data.term_insurer_policy_guides[0].title,
        guidePoint:
          data.term_insurer_policy_guides[0].term_insurer_policy_guide_points.map(
            (point) => ({
              title: point.title,
              description: point.description,
            })
          ),
      },
      seo: {
        metaTitle: data.term_insurer_seo.meta_title,
        metaDescription: data.term_insurer_seo.meta_description,
        keyword: data.term_insurer_seo.meta_keyword,
      },
      category: "term-insurance",
      ratings: data.term_insurer_ratings,
      statistics: statistics,
      claimSettlementPercentage: data.claim_settlement_ratio,
      networkHospitals:
        data.term_insurer_network_hospital_detail?.network_hospital_count,
    },
  };
};

export const transformCompanySlugData = (data: TermInsurerSlugData[]) => {
  return {
    data: data.map((item) => ({
      id: item.id,
      attributes: {
        name: item.name,
        slug: item.temp_slug,
        category: "term-insurance",
        hero: {
          title: item.name,
          rating: 0,
        },
        logo: {
          data: {
            attributes: {
              url: item.logo_url,
            },
          },
        },
        ratings: item.term_insurer_ratings[0],
      },
    })),
  };
};
