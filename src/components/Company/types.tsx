import { Meta } from "@/types";
import { AccordianType, Hero, <PERSON><PERSON> } from "../globals/types";

export type StatisticsProps = {
  companyName: string;
  graphs: Array<{
    title: string;
    industry: number;
    company: number;
    suffix: string;
    description?: string;
  }>;
  rating: Array<{
    title: string;
    rating: number;
    outOf: number;
  }>;
  ratingDescription?: string;
  networkHospitals?: number;
  claimSettlement?: number;
};

export interface Steps {
  data: {
    attributes: {
      steps: Array<AccordianType>;
    };
  };
}

export interface ClaimSettlement {
  title: string;
  cashless: Steps;
  reimbursement: Steps;
}

export interface Renewal {
  title: string;
  onlineRenewal: Steps;
  offlineRenewal: Steps;
}

export interface Plans {
  data: {
      id: number | string;
      attributes: {
        name: string;
        slug: string;
      };
    }[];
}

export interface PolicyGuide {
  title: string;
  guidePoint: GuidePoint[];
}

export interface GuidePoint {
  title: string;
  description: string;
}

export interface CompanyAttributes {
  name: string;
  slug: string;
  hero: <PERSON>;
  logo: <PERSON><PERSON>;
  verdict: string;
  pros: Array<AccordianType>;
  cons: Array<AccordianType>;
  kycDocs: Array<{
    name: string;
  }>;
  legecy: string;
  faqs: {
    question: string;
    ans: string;
  }[];
  renewalKeyPoints: Array<{ point: string }>;
  claimSettlement: ClaimSettlement;
  renewalSteps: Renewal;
  customerSupport: {
    email: string;
    contactNumber: string;
  };
  health_variants?: Plans;
  term_variants?: Plans;
  policyGuide?: PolicyGuide;
  seo?: {
    metaTitle: string;
    metaDescription: string;
    keyword: string;
  } | null;
  category: string;
  testimonials?: {
    id: number;
    testimonial: {
      id: number;
      name: string;
      statement: string;
      backgroundColor: string;
      thumbnail: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  ongoingOffer?: {
    title: string;
    descriptipn: string;
  };
  ratings: {
    id: number | string;
    solvency: number | null;
    icr: number | null;
    growth: number | null;
    aum: number | null;
  };
  statistics: {
    grossDirectPremium: {
      industry: number;
      company: number;
      description?: string;
    } | null;
    icr: {
      industry: number;
      company: number;
      description?: string;
    } | null;
    premiumUnderwritten: {
      industry: number;
      company: number;
      description?: string;
    } | null;
    solvencyRatio: {
      industry: number;
      company: number;
      description?: string;
    } | null;
  };
  claimSettlementPercentage: number;
  networkHospitals: number | string;
  ratingDescription?: string;
}

export type Company = {
  id: number | string;
  attributes: CompanyAttributes;
};

export type CompanyData = {
  data: Company[];
} & Meta;

export type TermInsurerApiResponse = {
  id: string;
  name: string;
  claim_settlement_ratio: number;
  logo_url: string;
  slug: string;
  temp_slug: string;
  term_insurer_claim_settlements: {
    id: string;
    title: string;
    term_insurer_claim_settlement_types: {
      id: string;
      title: string;
      type: "cashless claim" | "reimbursement claim";
      term_insurer_claim_settlement_steps: {
        id: string;
        title: string;
        description: string;
      }[];
    }[];
  }[];
  term_insurer_faqs: {
    id: string;
    question: string;
    answer: string;
  }[];
  term_insurer_policy_guides: {
    id: string;
    title: string;
    term_insurer_policy_guide_points: {
      id: string;
      title: string;
      description: string;
    }[];
  }[];
  term_insurer_pros_cons: {
    id: string;
    title: string;
    type: "pro" | "con";
    description: string;
  }[];
  term_insurer_ratings: {
    id: string;
    icr: number;
    growth: number;
    aum: number;
    one_assure_rating: number;
    solvency: number;
  };
  term_insurer_renewal_steps: {
    id: string;
    title: string;
    term_insurer_renewal_types: {
      id: string;
      title: string;
      type: "online renewal" | "offline renewal";
      term_insurer_renewal_type_steps: {
        id: string;
        title: string;
        description: string;
      }[];
    }[];
  }[];
  term_insurer_seo: {
    id: string;
    meta_description: string;
    meta_keyword: string;
    meta_title: string;
    source: string;
    prevent_indexing: boolean;
  };
  term_insurer_static_content: {
    id: string;
    verdict: string;
    legacy: string;
    kyc_docs: string[];
    renewal_key_points: string[];
    hero_title: string;
    customer_support_number: string;
    customer_support_email: string;
  };
  term_insurer_statistics: {
    id: string;
    industry: number;
    description: string;
    company: number;
    type: 'gross direct premium' | 'icr' | 'premium underwritten' | 'solvency ratio';
  }[];
  products: {
    product_variants: {
      id: string;
      variant_name: string;
      variant_slug: string;
      temp_slug: string;
    }[];
  }[];
  term_insurer_network_hospital_detail: {
    id: string;
    network_hospital_count: string;
    cities_covered: string;
    states_and_ut: string;
  };
};

export type TermInsurerSlugData = {
  id: string,
  name: string,
  slug: string,
  temp_slug: string,
  logo_url: string,
  term_insurer_ratings: [{
    aum: number;
    growth: number;
    icr: number;
    id: string;
    solvency: number;
  }];
}