// Individual item types for the API response
type ClaimProcessSpecification = {
    id: string;
    title: string;
    description: string;
    step: string;
    type: string;
    icon_url: string;
}

type ClaimSupportItem = {
    id: string;
    title: string;
    subtitle: string;
    icon_url: string;
}

type ClaimTestimonial = {
    id: string;
    name: string;
    statement: string;
    thumbnail_url: string;
    background_color: string;
}

// Main API response type
export type ClaimAPIResponse = {
    id: string;
    title: string;
    subtitle: string;
    testimonials_title: string;
    testimonials_subtitle: string;
    support_title: string;
    support_disclaimer: string;
    process_title: string;
    process_term_title: string;
    process_health_title: string;
    claim_page_process_specifications: ClaimProcessSpecification[];
    claim_page_support_items: ClaimSupportItem[];
    claim_page_testimonials: ClaimTestimonial[];
}

export function transformData(apiResponse: ClaimAPIResponse): any {
    const claimSupportItems = apiResponse.claim_page_support_items.map((item) => ({
        id: item.id,
        title: item.title,
        subtitle: item.subtitle,
        icon: {
            data: {
                id: item.id,
                attributes: {
                    url: item.icon_url,
                },
            },
        },
    }));
    const claimTestimonials = apiResponse.claim_page_testimonials.map((testimonial) => ({
        id: testimonial.id,
        name: testimonial.name,
        statement: testimonial.statement,
        backgroundColor: testimonial.background_color,
        thumbnail: {
            data: {
                id: testimonial.id,
                attributes: {
                    url: testimonial.thumbnail_url,
                },
            },
        },
    }));
    
    const claimProcessHealthCashless = apiResponse.claim_page_process_specifications.filter((specification) => specification.type === "cashless").map((specification) => ({
        id: specification.id,
        title: specification.title,
        desc: specification.description,
        step: specification.step,
        icon: {
            data: {
                id: specification.id,
                attributes: {
                    url: specification.icon_url,
                },
            },
        },
    }));

    const claimProcessHealthReimbursement = apiResponse.claim_page_process_specifications.filter((specification) => specification.type === "reimbursement").map((specification) => ({
        id: specification.id,
        title: specification.title,
        desc: specification.description,
        step: specification.step,
        icon: {
            data: {
                id: specification.id,
                attributes: {
                    url: specification.icon_url,
                },
            },
        },
    }));

    const claimProcessTermProcess = apiResponse.claim_page_process_specifications.filter((specification) => specification.type === "term").map((specification) => ({
        id: specification.id,
        title: specification.title,
        desc: specification.description,
        step: specification.step,
        icon: {
            data: {
                id: specification.id,
                attributes: {
                    url: specification.icon_url,
                },
            },
        },
    }));

    return {
        id: apiResponse.id,
        hero:{
            title: apiResponse.title,
            subtitle: apiResponse.subtitle,
        },
        support:{
            title: apiResponse.support_title,
            disclaimer: apiResponse.support_disclaimer,
            supportItems: claimSupportItems,
        },
        testimonials:{
            title: apiResponse.testimonials_title,
            subtitle: apiResponse.testimonials_subtitle,
            testimonial: claimTestimonials,
        },
        process:{
            title: apiResponse.process_title,
            healthTitle: apiResponse.process_health_title,
            termTitle: apiResponse.process_term_title,
            healthCashless: claimProcessHealthCashless,
            healthReimbursement: claimProcessHealthReimbursement,
            termProcess: claimProcessTermProcess,
        },
    }
}