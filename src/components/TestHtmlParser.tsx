import React from 'react';
import { htmlParser } from '@/utils/htmlParser';

const TestHtmlParser: React.FC = () => {
  const testHtml = `<p class="text-sm/[1.125rem] md:text-[1rem]/[1.5rem] font-normal text-neutral-800 animate-fade-in mt-2"><span style="background-color: transparent; color: rgb(0, 0, 0);">Pick a hospital tied up with your </span><strong style="background-color: transparent; color: rgb(0, 0, 0);">health insurance for senior citizens</strong><span style="background-color: transparent; color: rgb(0, 0, 0);">. You can check the insurer's website, mobile app, or contact customer support.</span></p>`;

  const parsedWithStyleStripping = htmlParser(testHtml, { stripStyles: true });
  const parsedWithoutStyleStripping = htmlParser(testHtml, { stripStyles: false });

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-2xl font-bold">HTML Parser Test</h1>
      
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Original HTML:</h2>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
          {testHtml}
        </pre>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Parsed with Style Stripping (stripStyles: true):</h2>
        <div className="border p-4 rounded">
          {parsedWithStyleStripping}
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Parsed without Style Stripping (stripStyles: false):</h2>
        <div className="border p-4 rounded">
          {parsedWithoutStyleStripping}
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Expected Behavior:</h2>
        <ul className="list-disc pl-6 space-y-2">
          <li>Both versions should render all text: "Pick a hospital tied up with your health insurance for senior citizens. You can check the insurer's website, mobile app, or contact customer support."</li>
          <li>The version with style stripping should remove inline styles from span and strong tags</li>
          <li>The version without style stripping should preserve inline styles</li>
        </ul>
      </div>
    </div>
  );
};

export default TestHtmlParser;
