"use client";
import React, { useState } from "react";
import { CompareIndexPageData } from "@/components/Compare/type";
import CompareIndexSections from "@/components/Compare/components/CompareIndexSections";
import CompareIndexFeatureSection from "@/components/Compare/components/CompareIndexFeatureSection";
import LeadForm from "@/components/globals/LeadForm";
import About from "@/components/Insurer/component/About";
import CompareIndexNote from "@/components/Compare/components/CompareIndexNote";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import CompareIndexHeroSection from "@/components/Compare/components/CompareIndexHeroSection";
import { ComparisonData } from "@/components/Compare/Dtos/unifiedDataTransformer";
import { ProductVariant } from "@/components/Compare/Dtos/unifiedDataTransformer";
import Container from "@/components/globals/Container";
import CompareIndexInsuranceCategory from "../components/CompareIndexInsuranceCategory";
import CompareIndexTopComparisons from "../components/CompareIndexTopComparisons";
import PageNavigation from "@/components/globals/PageNavigation";
import InsurerPlan from "@/components/globals/InsurerPlan";
import GoToTopFloater from "@/components/globals/GoToTopFloater";
import CalendlyFloater from "@/components/globals/CalendlyFloater";
import SharePageFloater from "@/components/globals/SharePageFloater";
import FAQSection from "@/components/globals/DSComponentsV0/FaqSection";


// // Categorize products by insurer
// const categorizeProducts = (products: ComparisonData[]) => {
//   const categories: { [key: string]: ComparisonData[] } = {};

//   if (!Array.isArray(products)) {
//     return categories;
//   }

//   products.forEach((product) => {
//     if (!product?.product_variant?.variant_slug) {
//       return;
//     }

//     const slug = product.product_variant.variant_slug;
//     let insurer = "";

//     // Extract insurer name from slug
//     if (slug.includes("niva-bupa")) insurer = "Niva Bupa";
//     else if (slug.includes("activ")) insurer = "Activ";
//     else if (slug.includes("icici")) insurer = "ICICI";
//     else if (slug.includes("care")) insurer = "Care";
//     else if (slug.includes("my-optima")) insurer = "My Optima";
//     else if (slug.includes("reassure")) insurer = "Reassure";
//     else insurer = "Other";

//     if (!categories[insurer]) {
//       categories[insurer] = [];
//     }
//     categories[insurer].push(product);
//   });

//   return categories;
// };

// // Generate all possible comparisons
// const generateComparisons = (products: ComparisonData[]) => {
//   const comparisons: Array<{
//     product1: ProductVariant;
//     product2: ProductVariant;
//     url: string;
//   }> = [];

//   if (!Array.isArray(products)) {
//     return comparisons;
//   }

//   for (let i = 0; i < products.length; i++) {
//     for (let j = i + 1; j < products.length; j++) {
//       const product1 = products[i]?.product_variant;
//       const product2 = products[j]?.product_variant;

//       if (
//         !product1?.variant_slug ||
//         !product2?.variant_slug ||
//         !product1?.id ||
//         !product2?.id
//       ) {
//         continue;
//       }

//       const url = `/compare-health-insurance-plans/${product1.product.insurer.slug}-${product1.variant_slug}-vs-${product2.product.insurer.slug}-${product2.variant_slug}/${product1.id}-${product2.id}`;

//       comparisons.push({ product1, product2, url });
//     }
//   }

//   return comparisons;
// };

const AllComparePage = ({
  compareIndexPageData,
  comparisonData,
  allInsurerData,
}: {
  compareIndexPageData: CompareIndexPageData;
  comparisonData: ComparisonData[];
  allInsurerData: any[];
}) => {
  const [activeTab, setActiveTab] = useState("top-comparisons");
  // Add loading state and error handling
  if (!comparisonData || !Array.isArray(comparisonData)) {
    return (
      <Container navSpacing={false} className="font-helvetica py-6 md:py-8">
        <div className="text-center px-4">
          <h1 className="text-xl md:text-2xl font-semibold text-gray-800 mb-3 md:mb-4">
            Loading Comparisons...
          </h1>
          <p className="text-sm md:text-base text-gray-600">
            Please wait while we load the comparison data.
          </p>
        </div>
      </Container>
    );
  }

  // const categorizedProducts = categorizeProducts(comparisonData);
  // const allComparisons = generateComparisons(comparisonData);

  return (
    <div>
      <CompareIndexHeroSection staticContent={compareIndexPageData.staticContent} comparisonData={comparisonData} />
      <PageNavigation
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        tabs={compareIndexPageData.pageNavigationSection.tabs}
      />
      <CompareIndexTopComparisons topComparisons={compareIndexPageData.topComparisons}/>
      <CompareIndexSections
        sections={compareIndexPageData.sections}
        type="assess_healthcare_need"
      />
      <CompareIndexFeatureSection
        features={compareIndexPageData.featuresToConsider}
        pill_content={compareIndexPageData.staticContent.pill_content}
      />
      <CompareIndexNote
        note={compareIndexPageData.staticContent.note}
        heading={"Note:"}
      />
      <LeadForm
        pill={compareIndexPageData.staticContent.pill_content}
        title="Still Confused ? Get Expert Advice"
        description="Our insurance experts are here to help you make the right choice. Get personalized recommendations based on your specific needs and budget."
      />
      <CompareIndexSections
        sections={compareIndexPageData.sections}
        type="read_inclusion_exclusion"
      />
      <CompareIndexSections
        sections={compareIndexPageData.sections}
        type="review_claim_settlement_ratio"
      />
      <CompareIndexSections
        sections={compareIndexPageData.sections}
        type="policy_conditions"
      />
      <CompareIndexSections
        sections={compareIndexPageData.sections}
        type="mistakes_to_avoid"
      />
      <SectionContainerMedium
        className="flex flex-col items-center !px-0 !mb-0"
      >
        <About
        about={compareIndexPageData.staticContent.need_to_compare_description}
        heading={compareIndexPageData.staticContent.need_to_compare_title}
          pill={compareIndexPageData.staticContent.pill_content}
        />
      </SectionContainerMedium>
      <LeadForm
        pill={compareIndexPageData.staticContent.pill_content}
        title="Still Confused ? Get Expert Advice"
        description="Our insurance experts are here to help you make the right choice. Get personalized recommendations based on your specific needs and budget."
      />
      <CompareIndexSections
        sections={compareIndexPageData.sections}
        type="why_chose_our_expert"
      />
      <CompareIndexSections
        sections={compareIndexPageData.sections}
        type="what_experts_help_you_with"
      />
      <CompareIndexInsuranceCategory insuranceCategories={compareIndexPageData.insuranceCategories}/>
      <FAQSection faq={compareIndexPageData.faqData} />

      {allInsurerData && allInsurerData.length > 0 && (
        <InsurerPlan allInsurerData={allInsurerData} />
      )}
      <SharePageFloater />
      <GoToTopFloater />
      <CalendlyFloater />
    </div>
  );
};

export default AllComparePage;
