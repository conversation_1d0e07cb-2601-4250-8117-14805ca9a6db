import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { BodyLarge } from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";


const CompareIndexNote = ({
  note,
  heading,
}: {
  note: string;
  heading: string;
}) => {
  const parsedNote = htmlParser(note, {
    classNames: {
      p: "text-neutral-1100",
    },
  });
  return (
    <SectionContainerMedium className="flex flex-col items-center !px-0">
      <div className="bg-primary-100 p-4 md:px-6 md:py-5 border border-primary-300 rounded-xl mx-6 md:mx-0">
        {/* <HeadingMedium
            as="h3"
            className="text-neutral-1100 text-center font-semibold mb-2"
        >
            {heading}
        </HeadingMedium> */}
        <div className="flex flex-col gap-2.5 ">
          <BodyLarge
            as="p"
            className="text-neutral-900 text-justify font-bold"
          >
            {heading}
          </BodyLarge>
          <BodyLarge>{parsedNote}</BodyLarge>
        </div>
      </div>
    </SectionContainerMedium>
  );
};

export default CompareIndexNote;
