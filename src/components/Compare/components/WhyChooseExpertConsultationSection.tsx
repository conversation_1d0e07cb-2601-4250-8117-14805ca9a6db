import React from "react";
import SectionContainer from "@/components/globals/SectionContainer";
import Image from "next/image";
import { HeadingSmall, HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { htmlParser } from "@/utils/htmlParser";

type CardData = {
  icon: string;
  title: string;
  description: string;
};

type WhyChooseExpertConsultationSectionProps = {
  heading: string;
  cards: CardData[];
};

const WhyChooseExpertConsultationSection: React.FC<
  WhyChooseExpertConsultationSectionProps
> = ({ heading, cards }) => {
  return (
    <SectionContainer className="flex flex-col items-center gap-4 md:gap-6 !p-0">
      {/* Desktop heading - inside the border */}
      <HeadingXLarge as="h2" className="text-neutral-1100 text-center px-6 md:p-0">
        {heading}
      </HeadingXLarge>

      <SectionContainerLarge className="hidden md:grid grid-cols-1 border border-primary-200 rounded-xl shadow-md md:rounded-none md:shadow-none md:border-none px-4 py-6 md:px-0 md:py-0 md:grid-cols-4 gap-6 !mb-0 md:!mb-0">
        {cards.map((card, idx) => (
          <div
            key={idx}
            className="flex flex-col items-center gap-y-3 md:gap-y-6 text-center "
          >
            <span
              className={`flex items-center justify-center w-16 h-16 rounded-full`}
              //${card.bgClass}
            >
              <Image
                src={card.icon}
                alt={card.title}
                width={64}
                height={64}
                className="w-16 h-16"
              />
            </span>
            <HeadingSmall weight="semibold" className="text-neutral-1100">
              {card.title}
            </HeadingSmall>
            <div>
              {htmlParser(card.description, {
                classNames: {
                  p: "text-neutral-800",
                },
              })}
            </div>
          </div>
        ))}
      </SectionContainerLarge>

      <MobileCarousel totalSlides={cards.length} className="md:hidden">
        {cards.map((card, idx) => (
          <MobileCarouselItem key={idx} className="px-4">
            <div className="flex flex-col items-center gap-y-3 md:gap-y-6 text-center">
              <span
                className={`flex items-center justify-center w-16 h-16 rounded-full`}
                //${card.bgClass}
              >
                <Image
                  src={card.icon}
                  alt={card.title}
                  width={64}
                  height={64}
                  className="w-16 h-16"
                />
              </span>
              <HeadingSmall weight="semibold" className="text-neutral-1100">
                {card.title}
              </HeadingSmall>
              <div>
                {htmlParser(card.description, {
                  classNames: {
                    p: "text-neutral-800",
                  },
                })}
              </div>
            </div>
          </MobileCarouselItem>
          )
        )}
      </MobileCarousel>
    </SectionContainer>
  );
};

export default WhyChooseExpertConsultationSection;
