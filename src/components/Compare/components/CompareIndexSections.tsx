import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import {
  BodyLarge,
  HeadingXLarge,
} from "@/components/UI/Typography";
import { CompareIndexPageSections } from "@/components/Compare/type";
import parse from "html-react-parser";
import { Grid } from "@/components/UI/Grid";
import PillBadge from "@/components/globals/PillBadge";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import MobileCarousel from "@/components/UI/MobileCarousel";
import SimpleCard from "@/components/globals/DSComponentsV0/SimpleCard";
import { FaShieldAlt } from "react-icons/fa";
import SectionHeader from "@/components/globals/SectionHeader";
import SectionHeaderWithParse from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
export default function CompareIndexSections({
  sections,
  pill_content,
  type,
}: {
  sections: CompareIndexPageSections[];
  pill_content?: string;
  type: string;
}) {
  if (!sections) return null;

  const section = sections.find((section) => section.type === type);
  if (!section) return null;

  const numPoints = section.compare_index_page_section_points.length;
  if (numPoints === 0) return null;

  return (
    <SectionContainerMedium className="flex flex-col items-center !px-0" id={type}>
      <SectionHeaderWithParse
        pill={pill_content}
        heading={section.title}
        subheading={section.description}
        component="h2"
      />
       
      {/* Desktop Grid Layout */}
      <Grid cols={numPoints >= 4 ? 4 : numPoints as 1 | 2 | 3} className="hidden md:grid">
        {section.compare_index_page_section_points.map((point) => (
          <SimpleCard key={point.id} title={point.title} description={point.description} icon={<FaShieldAlt className="text-tertiary-orange-400 w-6 h-6"/>} iconBgColor="bg-orange-100"/>
        ))}
      </Grid>

      {/* Mobile Carousel Layout */}
      <MobileCarousel totalSlides={numPoints} className="md:hidden">
        {section.compare_index_page_section_points.map((point) => (
          <MobileCarouselItem key={point.id}>
            <SimpleCard key={point.id} title={point.title} description={point.description} icon={<FaShieldAlt className="text-tertiary-orange-400 w-6 h-6"/>} iconBgColor="bg-orange-100"/>
          </MobileCarouselItem>
        ))}
      </MobileCarousel>
    </SectionContainerMedium>
  );
}
