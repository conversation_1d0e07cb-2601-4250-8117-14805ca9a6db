import React from 'react';
import { htmlParser } from './htmlParser';

// Simple test function to verify the fix
function testHtmlParser() {
  console.log('Testing HTML Parser...');

  // Test case 1: The problematic HTML
  const html1 = '<p>most <span><strong>people</strong></span></p>';
  const result1 = htmlParser(html1, {});
  console.log('Test 1 - Nested strong in span:', result1);

  // Test case 2: Mixed content
  const html2 = '<p>some text <span>with <strong>bold</strong> content</span> and more</p>';
  const result2 = htmlParser(html2, {});
  console.log('Test 2 - Mixed content:', result2);

  // Test case 3: Simple paragraph
  const html3 = '<p>simple paragraph</p>';
  const result3 = htmlParser(html3, {});
  console.log('Test 3 - Simple paragraph:', result3);

  // Test case 4: Only span in paragraph
  const html4 = '<p><span>only span content</span></p>';
  const result4 = htmlParser(html4, {});
  console.log('Test 4 - Only span:', result4);
}

// Export for manual testing
export { testHtmlParser };
