export function OpenCalendlyPopup() {
    const pathname = window.location.pathname;
    const utm_medium = sessionStorage.getItem("utm_medium");
    const utm_source = sessionStorage.getItem("utm_source");
    const utm_campaign = sessionStorage.getItem("utm_campaign");
    const utm_content = sessionStorage.getItem("utm_content");
    const utm_term = sessionStorage.getItem("utm_term");

    const script = document.createElement("script");
    script.src = "https://assets.calendly.com/assets/external/widget.js";
    script.async = true;
    document.head.appendChild(script);

    const link = document.createElement("link");
    link.href = "https://assets.calendly.com/assets/external/widget.css";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    script.onload = function () {
      if (window.Calendly) {
        const currentPath = pathname;
        const mediumParam = utm_medium
          ? `${currentPath}_${utm_medium}`
          : currentPath;

        window.Calendly.initPopupWidget({
          url: `https://calendly.com/oneassure/30min?year=2024&&utm_source=${utm_source}&utm_medium=${mediumParam}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`,
        });
      }
  };
  return false;
}