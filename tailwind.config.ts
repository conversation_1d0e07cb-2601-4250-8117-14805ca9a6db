import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "node_modules/flowbite-react/lib/esm/**/*.js",
  ],
  theme: {
    minWidth: {
      tabWidth: "192px",
      inputWidth: "420px",
    },
    minHeight: {
      tabHeight: "74px",
    },
    extend: {
      borderRadius: {
        "5xl": "2.5rem",
        "12xl": "6.25rem",
        "10xl": "5rem",
        "4xl": "1.25rem",
        lg: "var(--radius)",
        base: "0.5rem",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        generalSans: ["var(--font-generalSans)"],
        helvetica: ["Helvetica Neue", "Helvetica", "Arial", "sans-serif"],
        manrope: ["Manrope", "sans-serif"],
        inter: ["Inter", "sans-serif"],
        poppins: ["var(--font-poppins)"],
      },
      aspectRatio: {
        "0.658": "0.658",
      },
      boxShadow: {
        "300":
          "0px 2px 4px -1px rgba(0, 0, 0, 0.06), 0px 4px 15px 2px rgba(0, 0, 0, 0.10)",
        md: "0px 1.5px 1.5px 0px rgba(0, 0, 0, 0.25)",
        dark: "0px 2.5px 2.5px 0px rgba(0, 0, 0, 0.25)",
        shadow100:
          "0 2px 4px -1px rgba(0,0,0,0.06), 0 4px 15px 2px rgba(0,0,0,0.1)",
        recommendationAddMemberBtn:
          "0 1px 10px 0 rgba(0, 0, 0, 0.05), 0 1px 20px 0 rgba(0, 0, 0, 0.04)",
        productCard: "0 2px 10px 0 #0000000d, 0 2px 20px 0 #0000000d",
        workDashboardCard: "0 2px 4px -1px #0000000d, 0 4px 15px 2px #0000000d",
        workSection: "0 1px 10px 0 rgba(0, 0, 0, 0.05), 0 1px 20px 0 #0000000d",
        whyOneassure: "#55B5AA26",
        customblue4d: "0px 4px 4px 0px #3B71CA4D",
        customgreen4d: "0px 4px 4px 0px #0000001A",
        BookACall: "0px 2px 4px 0px #6B6B6B",
      },
      colors: {
        blue100: "#e5e5ff",
        blue200: "#ccccff",
        blue300: "#9999ff",
        blue400: "#6666ff",
        blue500: "#3333ff",
        blue600: "#0000ff",
        blue700: "#5DA3D0",
        blue800: "#469DD7",
        blue900: "#469DD7",
        blue1000: "#2373A8",
        blue1100: "#136093",
        blue1200: "#117CC299",
        blue1300: "#117CC2CC",
        gray100: "#C6C6C6",
        gray200: "#ebecf2",
        gray300: "#d2d4d9",
        gray400: "#a9b4bf",
        gray500: "#939ca6",
        gray600: "#7c848c",
        gray700: "#666d73",
        gray800: "#505559",
        gray900: "#222426",
        gray1000: "#EFF1F9",
        gray1100: "#CFD3D4",
        gray1200: "#5E6366",
        gray1300: "#EFF1F999",
        gray1400: "#999999",
        orange100: "#f1d4ae",
        orange200: "#f1c88e",
        orange300: "#f1ba6f",
        orange400: "#f1ad50",
        orange500: "#f1a030",
        orange600: "#f19311",
        green100: "#cafce0",
        green200: "#a5f0c5",
        green300: "#10947e",
        green400: "#00917c",
        green500: "#035956",
        green600: "#184d47",
        green700: "#55B5AA",
        green800: "#4CA2B0",
        green900: "#89EDFF",
        green1000: "#1A9F8F99",
        green1100: "#88C8D3",
        green1200: "#1A9F8FCC",
        green1300: "#78C478",
        red100: "#fbebeb",
        red200: "#f6d7d7",
        red300: "#edafaf",
        red400: "#e48686",
        red500: "#db5e5e",
        red600: "#d23636",
        slateGrey: "#666c73",
        grayBg: "#fafafa",
        darkBlueBg: "#00001c",
        bgYellow: "#fffcf1",
        bgOrange: "#fff9f1",
        bgPurple: "#f3f1ff",
        bgGreen: "#f1fff8",
        bgRed: "#fff1f1",
        lightPurple: "#4A4AFFCC",
        purple100: "#5676FF",
        black200: "#1A9F8F",
        leftGreen: "#1A9F8F",
        rightBlue: "#117CC2",
        circleWhite: "#FEFEFE",
        backgroundWhite: "#FFFFFF",
        darkGreen: "#50ABBB",
        black100: "#2E2E2E",
        black300: "#000000",
        black400: "#000000",
        cardBackground: "#FFFFFF4D",

        // --------------NEW COLOR PALLETTE-----------------

        // --Neutral--
        "ntrl-black": "#222226",
        "ntrl-grey1": "#677B92",
        "ntrl-grey2": "#DBDDEF",
        "ntrl-outline": "#EDEEF7",
        "ntrl-white": "#FFFFFF",

        // --Primaries--
        "primary-1": "#0000A3",
        "primary-2": "#0000FF",
        "primary-3": "#EBF2FF",

        // --Secondaries--
        "secondary-1": "#199F8F",
        "secondary-2": "#EDFCFB",

        //comparison page colors

        "green-100":"#00A991",
        "green-main-200":"#64C8BC",
        "green-50":"#E6F6F4",

        "neutral-dark-1100":"#243547",
        "neutral-dark-900":"#40566D",
        "neutral-dark-800":"#58728D",
        "neutral-light-700":"#6C849D",

        "blue-700":"#056AB6",
        "blue-main-800":"#045591",
        "blue-100":"#E6F4FE",
        "blue-300":"#B2DCFB",

        "gray-100":"#F1F5FA",






        // --Reds--
        "red-1": "#D23636",
        "red-2": "#E48686",
        "red-3": "#F6D7D7",

        // --Oranges--
        "orange-1": "#E78D0D",
        "orange-2": "#F1AD50",
        "orange-3": "#F1D4AD",

        // --Blues--
        "blue-1": "#00548B",
        "blue-2": "#3F8CBF",
        "blue-3": "#BFE6FF",
        "blue-4": "#EEF6FC",
        "blue-5": "#2769F6",

        // --Greens--
        "green-1": "#71A725",
        "green-2": "#A7D662",
        "green-3": "#E9FCCF",
        "green-4": "#184D47",
        "green-5": "#1BA500",

        // --Backgrounds--
        "soft-blue": "#F1F6FF",
        "soft-grey": "#F2F2F2",
        "sky-grey": "#E7EFFF",

        // --------------LATEST COLOR PALLETTE-----------------
        //      --Primary--
        "primary-green-1": "#EDFCFB",
        "primary-green-2": "#48B2A5",
        "primary-blue-1": "#EBF2FF",
        "primary-blue-2": "#C6E0F0",
        "primary-blue-3": "#2989C8",
        //  --Secondary--
        "secondary-blue-1": "#53A3D0",
        "secondary-blue-2": "#50A9BF",
        "secondary-green-1": "#50ABBB",
        "secondary-green-2": "#55B5AA",
        "secondary-green-3": "#51A7B6",

        //  --Gradients--
        "gradient-1-green": "#53B6AC",
        "gradient-1-blue": "#4D9CCE",
        "gradient-2-blue-dark": "#2475A9",
        "gradient-2-blue-light": "#449BD4",
        "gradient-3-green-dark": "#4DA2B1",
        "gradient-3-green-light": "#6AC6D7",
        "gradient-4-blue-dark": "#5C9ECA",
        "gradient-4-blue-light": "#6BB0DE",
        //  --Neutrals--
        "ntrl-black-1": "#18181B",
        "ntrl-black-2": "#2A2A2A",
        "ntrl-grey-1": "#F6F7FB",
        "ntrl-grey-2": "#F5F5F5",
        "ntrl-grey-3": "#EFF1F9",
        "ntrl-grey-4": "#CFD3D4",
        "ntrl-grey-5": "#999999",
        //  --Logo--
        "logo-1": "#2E56FF",
        "logo-2": "#1EBEAB",


		// --------------FINAL DESIGN SYSTEM PALETTE-----------------

		// ---Primary---	
    "primary-50":"#f8fbff",
		"primary-100":"#e6f4fe",
		"primary-200":"#daeefd",
		"primary-300":"#b2dcfb",
		"primary-400":"#078df2",
		"primary-500":"#067fda",
		"primary-600":"#0671c2",
		"primary-700":"#056ab6",
		"primary-800":"#045591",
		"primary-900":"#033f6d",
		"primary-1000":"#023155",


		//----Secondary---

		"secondary-100":"#e6f6f4",
		"secondary-200":"#d9f2ef",
		"secondary-300":"#b0e4dd",
		"secondary-400":"#00a991",
		"secondary-500":"#009883",
		"secondary-600":"#008774",
		"secondary-700":"#007f6d",
		"secondary-800":"#006557",
		"secondary-900":"#004c41",
		"secondary-1000":"#003b33",


    //---additional colors---

    "tertiary-red-400":"#E7000B",
    "tertiary-orange-400":"#F26C07",

		//----Neutral---

		"neutral-1300":"#0C1927",
		"neutral-1200":"#192839",
		"neutral-1100":"#243547",
		"neutral-1000":"#2F4256",
		"neutral-900":"#40566D",
		"neutral-800":"#58728D",
		"neutral-700":"#6C849D",
		"neutral-600":"#768EA7",
		"neutral-500":"#90A5BB",
		"neutral-400":"#B1C1D2",
		"neutral-300":"#CBD5E2",
		"neutral-200":"#E3EAF3",
		"neutral-100":"#F1F5FA",
		"neutral-050":"#F8FAFC",
		"neutral-000":"#FCFCFD",



        letterSpacing: {
          tightest: "0.015em",
        },
        fontSize: {
          "custom-32": {
            fontSize: "32px",
            lineHeight: "36px",
          },
        },

		background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
      },
      keyframes: {
        "slide-in-up": {
          "0%": {
            opacity: "0",
            transform: "translateY(20px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "bounce-gentle": {
          "0%, 100%": {
            transform: "translateY(0)",
          },
          "50%": {
            transform: "translateY(-4px)",
          },
        },
        pulse: {
          "0%, 100%": {
            opacity: "1",
          },
          "50%": {
            opacity: "0.7",
          },
        },
        float: {
          "0%, 100%": {
            transform: "translateY(0)",
          },
          "50%": {
            transform: "translateY(-10px)",
          },
        },
        "float-slow": {
          "0%, 100%": {
            transform: "translateY(0)",
          },
          "50%": {
            transform: "translateY(-15px)",
          },
        },
      },
      animation: {
        "slide-in-up": "slide-in-up 0.5s ease-out",
        "bounce-gentle": "bounce-gentle 1.5s infinite",
        pulse: "pulse 2s infinite",
        float: "float 3s ease-in-out infinite",
        "float-slow": "float-slow 5s ease-in-out infinite",
      },
    },
  },
  plugins: [require("flowbite/plugin")],
};
export default config;
