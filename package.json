{"name": "oneassure-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@lottiefiles/react-lottie-player": "^3.5.3", "@next/third-parties": "^14.2.1", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "animate.css": "^4.1.1", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.11", "embla-carousel-react": "^8.1.3", "flowbite-react": "^0.7.2", "formik": "^2.4.5", "html-react-parser": "^5.2.5", "lucide-react": "^0.536.0", "markdown-to-jsx": "^7.4.7", "next": "14.2.0", "next-sitemap": "^4.2.3", "react": "^18", "react-dom": "^18", "react-fast-marquee": "^1.6.3", "react-hook-form": "^7.61.1", "react-icons": "^5.0.1", "react-markdown": "^9.1.0", "react-toastify": "^11.0.2", "react-window": "^1.8.11", "react-youtube": "^10.1.0", "remark-gfm": "^4.0.1", "sharp": "^0.33.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "uuid": "^11.1.0", "yup": "^1.4.0", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-window": "^1.8.8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}