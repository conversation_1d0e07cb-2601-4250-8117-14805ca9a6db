<!DOCTYPE html>
<html>
<head>
    <title>HTML Parser Test</title>
</head>
<body>
    <h1>HTML Parser Test Cases</h1>
    
    <h2>Test Case 1: Original Problem</h2>
    <p>HTML: <code>&lt;p&gt;most &lt;span&gt;&lt;strong&gt;people&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;</code></p>
    <p>Expected: Should render both "most" and "people"</p>
    
    <h2>Test Case 2: Mixed Content</h2>
    <p>HTML: <code>&lt;p&gt;some text &lt;span&gt;with &lt;strong&gt;bold&lt;/strong&gt; content&lt;/span&gt; and more&lt;/p&gt;</code></p>
    <p>Expected: Should render all text including "some text", "with", "bold", "content", "and more"</p>
    
    <h2>Test Case 3: Simple Paragraph</h2>
    <p>HTML: <code>&lt;p&gt;simple paragraph&lt;/p&gt;</code></p>
    <p>Expected: Should render "simple paragraph"</p>
    
    <h2>Test Case 4: Only Span in Paragraph</h2>
    <p>HTML: <code>&lt;p&gt;&lt;span&gt;only span content&lt;/span&gt;&lt;/p&gt;</code></p>
    <p>Expected: Should render "only span content" (span extraction should still work)</p>
    
    <h2>Test Case 5: Style Stripping</h2>
    <p>HTML: <code>&lt;p class="..."&gt;&lt;span style="background-color: transparent; color: rgb(0, 0, 0);"&gt;Pick a hospital tied up with your &lt;/span&gt;&lt;strong style="background-color: transparent; color: rgb(0, 0, 0);"&gt;health insurance for senior citizens&lt;/strong&gt;&lt;span style="background-color: transparent; color: rgb(0, 0, 0);"&gt;. You can check the insurer's website, mobile app, or contact customer support.&lt;/span&gt;&lt;/p&gt;</code></p>
    <p>Expected: Should strip inline styles from span and strong tags when stripStyles=true</p>

    <h2>The Fixes</h2>
    <h3>Fix 1: Span Extraction Logic</h3>
    <p>The issue was in the span extraction logic. The parser was looking for any span child and extracting only its content, ignoring other siblings. The fix changes the condition from <code>domNode.children?.length > 0</code> to <code>domNode.children?.length === 1</code>, so span extraction only happens when the span is the ONLY child element.</p>

    <h3>Fix 2: Style Stripping for Tags Without Components</h3>
    <p>Added logic to strip styles from tags that don't have custom components defined (like span and strong). Previously, these tags would fall through to the default return and keep their original styles intact.</p>
</body>
</html>
