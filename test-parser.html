<!DOCTYPE html>
<html>
<head>
    <title>HTML Parser Test</title>
</head>
<body>
    <h1>HTML Parser Test Cases</h1>
    
    <h2>Test Case 1: Original Problem</h2>
    <p>HTML: <code>&lt;p&gt;most &lt;span&gt;&lt;strong&gt;people&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;</code></p>
    <p>Expected: Should render both "most" and "people"</p>
    
    <h2>Test Case 2: Mixed Content</h2>
    <p>HTML: <code>&lt;p&gt;some text &lt;span&gt;with &lt;strong&gt;bold&lt;/strong&gt; content&lt;/span&gt; and more&lt;/p&gt;</code></p>
    <p>Expected: Should render all text including "some text", "with", "bold", "content", "and more"</p>
    
    <h2>Test Case 3: Simple Paragraph</h2>
    <p>HTML: <code>&lt;p&gt;simple paragraph&lt;/p&gt;</code></p>
    <p>Expected: Should render "simple paragraph"</p>
    
    <h2>Test Case 4: Only Span in Paragraph</h2>
    <p>HTML: <code>&lt;p&gt;&lt;span&gt;only span content&lt;/span&gt;&lt;/p&gt;</code></p>
    <p>Expected: Should render "only span content" (span extraction should still work)</p>
    
    <h2>The Fix</h2>
    <p>The issue was in the span extraction logic. The parser was looking for any span child and extracting only its content, ignoring other siblings. The fix changes the condition from <code>domNode.children?.length > 0</code> to <code>domNode.children?.length === 1</code>, so span extraction only happens when the span is the ONLY child element.</p>
</body>
</html>
